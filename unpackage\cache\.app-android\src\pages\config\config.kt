@file:Suppress("UNCHECKED_CAST", "USELESS_CAST", "INAPPLICABLE_JVM_NAME", "UNUSED_ANONYMOUS_PARAMETER", "NAME_SHADOWING", "UNNECESSARY_NOT_NULL_ASSERTION")
package uni.UNI666077E
import io.dcloud.uniapp.*
import io.dcloud.uniapp.extapi.*
import io.dcloud.uniapp.framework.*
import io.dcloud.uniapp.runtime.*
import io.dcloud.uniapp.vue.*
import io.dcloud.uniapp.vue.shared.*
import io.dcloud.unicloud.*
import io.dcloud.uts.*
import io.dcloud.uts.Map
import io.dcloud.uts.Set
import io.dcloud.uts.UTSAndroid
import io.dcloud.uniapp.extapi.getStorageSync as uni_getStorageSync
import io.dcloud.uniapp.extapi.navigateBack as uni_navigateBack
import io.dcloud.uniapp.extapi.setStorageSync as uni_setStorageSync
import io.dcloud.uniapp.extapi.showToast as uni_showToast
open class GenPagesConfigConfig : BasePage {
    constructor(__ins: ComponentInternalInstance, __renderer: String?) : super(__ins, __renderer) {
        onLoad(fun(_: OnLoadOptions) {
            this.loadConfig()
        }
        , __ins)
    }
    @Suppress("UNUSED_PARAMETER", "UNUSED_VARIABLE")
    override fun `$render`(): Any? {
        val _ctx = this
        val _cache = this.`$`.renderCache
        val _component_switch = resolveComponent("switch")
        return _cE("view", _uM("class" to "container"), _uA(
            _cE("view", _uM("class" to "header"), _uA(
                _cE("button", _uM("class" to "back-btn", "onClick" to _ctx.goBack), "返回", 8, _uA(
                    "onClick"
                )),
                _cE("text", _uM("class" to "header-title"), "WebDAV配置"),
                _cE("view", _uM("class" to "header-placeholder"))
            )),
            _cE("scroll-view", _uM("class" to "config-content", "scroll-y" to "true"), _uA(
                _cE("view", _uM("class" to "form-container"), _uA(
                    _cE("view", _uM("class" to "form-section"), _uA(
                        _cE("text", _uM("class" to "section-title"), "服务器设置"),
                        _cE("view", _uM("class" to "form-item"), _uA(
                            _cE("text", _uM("class" to "form-label"), "服务器地址"),
                            _cE("input", _uM("class" to "form-input", "modelValue" to _ctx.config["serverUrl"], "onInput" to fun(`$event`: UniInputEvent){
                                _ctx.config["serverUrl"] = `$event`.detail.value
                            }
                            , "placeholder" to "https://your-webdav-server.com", "type" to "url"), null, 40, _uA(
                                "modelValue",
                                "onInput"
                            ))
                        )),
                        _cE("view", _uM("class" to "form-item"), _uA(
                            _cE("text", _uM("class" to "form-label"), "端口号"),
                            _cE("input", _uM("class" to "form-input", "modelValue" to _ctx.config["port"], "onInput" to fun(`$event`: UniInputEvent){
                                _ctx.config["port"] = `$event`.detail.value
                            }
                            , "placeholder" to "443", "type" to "number"), null, 40, _uA(
                                "modelValue",
                                "onInput"
                            ))
                        ))
                    )),
                    _cE("view", _uM("class" to "form-section"), _uA(
                        _cE("text", _uM("class" to "section-title"), "认证信息"),
                        _cE("view", _uM("class" to "form-item"), _uA(
                            _cE("text", _uM("class" to "form-label"), "用户名"),
                            _cE("input", _uM("class" to "form-input", "modelValue" to _ctx.config["username"], "onInput" to fun(`$event`: UniInputEvent){
                                _ctx.config["username"] = `$event`.detail.value
                            }
                            , "placeholder" to "请输入用户名", "type" to "text"), null, 40, _uA(
                                "modelValue",
                                "onInput"
                            ))
                        )),
                        _cE("view", _uM("class" to "form-item"), _uA(
                            _cE("text", _uM("class" to "form-label"), "密码"),
                            _cE("input", _uM("class" to "form-input", "modelValue" to _ctx.config["password"], "onInput" to fun(`$event`: UniInputEvent){
                                _ctx.config["password"] = `$event`.detail.value
                            }
                            , "placeholder" to "请输入密码", "type" to "password"), null, 40, _uA(
                                "modelValue",
                                "onInput"
                            ))
                        ))
                    )),
                    _cE("view", _uM("class" to "form-section"), _uA(
                        _cE("text", _uM("class" to "section-title"), "高级设置"),
                        _cE("view", _uM("class" to "form-item"), _uA(
                            _cE("text", _uM("class" to "form-label"), "根目录"),
                            _cE("input", _uM("class" to "form-input", "modelValue" to _ctx.config["rootPath"], "onInput" to fun(`$event`: UniInputEvent){
                                _ctx.config["rootPath"] = `$event`.detail.value
                            }
                            , "placeholder" to "/movies", "type" to "text"), null, 40, _uA(
                                "modelValue",
                                "onInput"
                            ))
                        )),
                        _cE("view", _uM("class" to "form-item"), _uA(
                            _cE("view", _uM("class" to "form-row"), _uA(
                                _cE("text", _uM("class" to "form-label"), "启用HTTPS"),
                                _cV(_component_switch, _uM("class" to "form-switch", "checked" to _ctx.config["useHttps"]), null, 8, _uA(
                                    "checked"
                                ))
                            ))
                        )),
                        _cE("view", _uM("class" to "form-item"), _uA(
                            _cE("view", _uM("class" to "form-row"), _uA(
                                _cE("text", _uM("class" to "form-label"), "自动连接"),
                                _cV(_component_switch, _uM("class" to "form-switch", "checked" to _ctx.config["autoConnect"]), null, 8, _uA(
                                    "checked"
                                ))
                            ))
                        ))
                    )),
                    _cE("view", _uM("class" to "button-group"), _uA(
                        _cE("button", _uM("class" to "test-btn", "onClick" to _ctx.testConnection), "测试连接", 8, _uA(
                            "onClick"
                        )),
                        _cE("button", _uM("class" to "save-btn", "onClick" to _ctx.saveConfig), "保存配置", 8, _uA(
                            "onClick"
                        ))
                    )),
                    if (isTrue(_ctx.connectionStatus)) {
                        _cE("view", _uM("key" to 0, "class" to "status-section"), _uA(
                            _cE("text", _uM("class" to _nC(_uA(
                                "status-text",
                                _ctx.statusClass
                            ))), _tD(_ctx.connectionStatus), 3)
                        ))
                    } else {
                        _cC("v-if", true)
                    }
                ))
            ))
        ))
    }
    open var config: UTSJSONObject by `$data`
    open var connectionStatus: String by `$data`
    open var statusClass: String by `$data`
    @Suppress("USELESS_CAST")
    override fun data(): Map<String, Any?> {
        return _uM("config" to object : UTSJSONObject() {
            var serverUrl = ""
            var port = "443"
            var username = ""
            var password = ""
            var rootPath = "/movies"
            var useHttps = true
            var autoConnect = false
        }, "connectionStatus" to "", "statusClass" to "")
    }
    open var goBack = ::gen_goBack_fn
    open fun gen_goBack_fn() {
        uni_navigateBack(null)
    }
    open var loadConfig = ::gen_loadConfig_fn
    open fun gen_loadConfig_fn() {
        try {
            val savedConfig = uni_getStorageSync("webdav_config")
            if (savedConfig != null) {
                val configData = savedConfig as UTSJSONObject
                this.config["serverUrl"] = configData["serverUrl"] as String ?: ""
                this.config["port"] = configData["port"] as String ?: "443"
                this.config["username"] = configData["username"] as String ?: ""
                this.config["password"] = configData["password"] as String ?: ""
                this.config["rootPath"] = configData["rootPath"] as String ?: "/movies"
                this.config["useHttps"] = configData["useHttps"] as Boolean ?: true
                this.config["autoConnect"] = configData["autoConnect"] as Boolean ?: false
            }
        }
         catch (e: Throwable) {
            console.error(e as Any, " at pages/config/config.uvue:159")
        }
    }
    open var saveConfig = ::gen_saveConfig_fn
    open fun gen_saveConfig_fn() {
        if (this.config["serverUrl"] == null || (this.config["serverUrl"] as String).trim() == "") {
            uni_showToast(ShowToastOptions(title = "请输入服务器地址", icon = "none"))
            return
        }
        if (this.config["username"] == null || (this.config["username"] as String).trim() == "") {
            uni_showToast(ShowToastOptions(title = "请输入用户名", icon = "none"))
            return
        }
        try {
            uni_setStorageSync("webdav_config", this.config)
            uni_showToast(ShowToastOptions(title = "配置保存成功", icon = "success"))
        }
         catch (e: Throwable) {
            uni_showToast(ShowToastOptions(title = "保存失败", icon = "error"))
            console.error(e as Any, " at pages/config/config.uvue:193")
        }
    }
    open var testConnection = ::gen_testConnection_fn
    open fun gen_testConnection_fn(): UTSPromise<Unit> {
        return wrapUTSPromise(suspend w@{
                if ((this.config["serverUrl"] == null || (this.config["serverUrl"] as String).trim() == "") || (this.config["username"] == null || (this.config["username"] as String).trim() == "")) {
                    uni_showToast(ShowToastOptions(title = "请先填写服务器地址和用户名", icon = "none"))
                    return@w
                }
                this.connectionStatus = "正在测试连接..."
                this.statusClass = "status-testing"
        })
    }
    companion object {
        val styles: Map<String, Map<String, Map<String, Any>>> by lazy {
            _nCS(_uA(
                styles0
            ), _uA(
                GenApp.styles
            ))
        }
        val styles0: Map<String, Map<String, Map<String, Any>>>
            get() {
                return _uM("container" to _pS(_uM("flex" to 1, "backgroundColor" to "#1a1a1a")), "header" to _pS(_uM("display" to "flex", "justifyContent" to "space-between", "alignItems" to "center", "paddingTop" to 20, "paddingRight" to 20, "paddingBottom" to 20, "paddingLeft" to 20, "backgroundColor" to "#2d2d2d", "borderBottomWidth" to 1, "borderBottomStyle" to "solid", "borderBottomColor" to "#404040")), "back-btn" to _pS(_uM("backgroundColor" to "#404040", "color" to "#ffffff", "borderTopWidth" to "medium", "borderRightWidth" to "medium", "borderBottomWidth" to "medium", "borderLeftWidth" to "medium", "borderTopStyle" to "none", "borderRightStyle" to "none", "borderBottomStyle" to "none", "borderLeftStyle" to "none", "borderTopColor" to "#000000", "borderRightColor" to "#000000", "borderBottomColor" to "#000000", "borderLeftColor" to "#000000", "borderTopLeftRadius" to 8, "borderTopRightRadius" to 8, "borderBottomRightRadius" to 8, "borderBottomLeftRadius" to 8, "paddingTop" to 8, "paddingRight" to 16, "paddingBottom" to 8, "paddingLeft" to 16, "fontSize" to 14, "cursor" to "pointer", "backgroundColor:hover" to "#505050")), "header-title" to _pS(_uM("fontSize" to 20, "fontWeight" to "bold", "color" to "#ffffff")), "header-placeholder" to _pS(_uM("width" to 60)), "config-content" to _pS(_uM("flex" to 1, "paddingTop" to 20, "paddingRight" to 20, "paddingBottom" to 20, "paddingLeft" to 20)), "form-container" to _pS(_uM("maxWidth" to 600, "marginTop" to 0, "marginRight" to "auto", "marginBottom" to 0, "marginLeft" to "auto")), "form-section" to _pS(_uM("marginBottom" to 30, "backgroundColor" to "#2d2d2d", "borderTopLeftRadius" to 12, "borderTopRightRadius" to 12, "borderBottomRightRadius" to 12, "borderBottomLeftRadius" to 12, "paddingTop" to 20, "paddingRight" to 20, "paddingBottom" to 20, "paddingLeft" to 20)), "section-title" to _pS(_uM("fontSize" to 18, "fontWeight" to "bold", "color" to "#ffffff", "marginBottom" to 20)), "form-item" to _pS(_uM("marginBottom" to 20, "marginBottom:last-child" to 0)), "form-label" to _pS(_uM("fontSize" to 16, "color" to "#cccccc", "marginBottom" to 8)), "form-input" to _pS(_uM("width" to "100%", "paddingTop" to 12, "paddingRight" to 16, "paddingBottom" to 12, "paddingLeft" to 16, "backgroundColor" to "#404040", "borderTopWidth" to 1, "borderRightWidth" to 1, "borderBottomWidth" to 1, "borderLeftWidth" to 1, "borderTopStyle" to "solid", "borderRightStyle" to "solid", "borderBottomStyle" to "solid", "borderLeftStyle" to "solid", "borderTopColor" to "#555555", "borderRightColor" to "#555555", "borderBottomColor" to "#555555", "borderLeftColor" to "#555555", "borderTopLeftRadius" to 8, "borderTopRightRadius" to 8, "borderBottomRightRadius" to 8, "borderBottomLeftRadius" to 8, "color" to "#ffffff", "fontSize" to 16, "borderTopColor:focus" to "#007AFF", "borderRightColor:focus" to "#007AFF", "borderBottomColor:focus" to "#007AFF", "borderLeftColor:focus" to "#007AFF", "outline:focus" to "none")), "form-row" to _pS(_uM("display" to "flex", "justifyContent" to "space-between", "alignItems" to "center")), "form-switch" to _pS(_uM("transform" to "scale(1.2)")), "button-group" to _pS(_uM("display" to "flex", "gap" to "15px", "marginTop" to 30)), "test-btn" to _pS(_uM("flex" to 1, "paddingTop" to 15, "paddingRight" to 15, "paddingBottom" to 15, "paddingLeft" to 15, "borderTopWidth" to "medium", "borderRightWidth" to "medium", "borderBottomWidth" to "medium", "borderLeftWidth" to "medium", "borderTopStyle" to "none", "borderRightStyle" to "none", "borderBottomStyle" to "none", "borderLeftStyle" to "none", "borderTopColor" to "#000000", "borderRightColor" to "#000000", "borderBottomColor" to "#000000", "borderLeftColor" to "#000000", "borderTopLeftRadius" to 8, "borderTopRightRadius" to 8, "borderBottomRightRadius" to 8, "borderBottomLeftRadius" to 8, "fontSize" to 16, "fontWeight" to "bold", "cursor" to "pointer", "backgroundColor" to "#FF9500", "color" to "#ffffff", "backgroundColor:hover" to "#E6850E")), "save-btn" to _pS(_uM("flex" to 1, "paddingTop" to 15, "paddingRight" to 15, "paddingBottom" to 15, "paddingLeft" to 15, "borderTopWidth" to "medium", "borderRightWidth" to "medium", "borderBottomWidth" to "medium", "borderLeftWidth" to "medium", "borderTopStyle" to "none", "borderRightStyle" to "none", "borderBottomStyle" to "none", "borderLeftStyle" to "none", "borderTopColor" to "#000000", "borderRightColor" to "#000000", "borderBottomColor" to "#000000", "borderLeftColor" to "#000000", "borderTopLeftRadius" to 8, "borderTopRightRadius" to 8, "borderBottomRightRadius" to 8, "borderBottomLeftRadius" to 8, "fontSize" to 16, "fontWeight" to "bold", "cursor" to "pointer", "backgroundColor" to "#007AFF", "color" to "#ffffff", "backgroundColor:hover" to "#0056CC")), "status-section" to _pS(_uM("marginTop" to 20, "textAlign" to "center")), "status-text" to _pS(_uM("fontSize" to 16, "fontWeight" to "bold", "paddingTop" to 12, "paddingRight" to 20, "paddingBottom" to 12, "paddingLeft" to 20, "borderTopLeftRadius" to 8, "borderTopRightRadius" to 8, "borderBottomRightRadius" to 8, "borderBottomLeftRadius" to 8)), "status-testing" to _pS(_uM("backgroundColor" to "#FF9500", "color" to "#ffffff")), "status-success" to _pS(_uM("backgroundColor" to "#34C759", "color" to "#ffffff")), "status-error" to _pS(_uM("backgroundColor" to "#FF3B30", "color" to "#ffffff")))
            }
        var inheritAttrs = true
        var inject: Map<String, Map<String, Any?>> = _uM()
        var emits: Map<String, Any?> = _uM()
        var props = _nP(_uM())
        var propsNeedCastKeys: UTSArray<String> = _uA()
        var components: Map<String, CreateVueComponent> = _uM()
    }
}
