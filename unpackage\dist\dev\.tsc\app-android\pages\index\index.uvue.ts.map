{"version": 3, "file": "pages/index/index.uvue", "names": [], "sources": ["pages/index/index.uvue"], "sourcesContent": ["<template>\r\n\t<view class=\"container\">\r\n\t\t<!-- 顶部导航栏 -->\r\n\t\t<view class=\"header\">\r\n\t\t\t<text class=\"header-title\">电影库</text>\r\n\t\t\t<view class=\"header-actions\">\r\n\t\t\t\t<button class=\"config-btn\" @click=\"goToConfig\">设置</button>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\r\n\t\t<!-- 电影海报网格 -->\r\n\t\t<scroll-view class=\"movie-grid\" scroll-y=\"true\">\r\n\t\t\t<view class=\"grid-container\">\r\n\t\t\t\t<view\n\t\t\t\t\tclass=\"movie-card\"\n\t\t\t\t\tv-for=\"(movie, index) in movies\"\n\t\t\t\t\t:key=\"index\"\n\t\t\t\t\t@click=\"selectMovie(movie)\"\n\t\t\t\t>\n\t\t\t\t\t<image\n\t\t\t\t\t\tclass=\"movie-poster\"\n\t\t\t\t\t\t:src=\"movie.poster\"\n\t\t\t\t\t\tmode=\"aspectFill\"\n\t\t\t\t\t/>\n\t\t\t\t\t<view class=\"movie-info\">\n\t\t\t\t\t\t<text class=\"movie-title\">{{movie.title}}</text>\n\t\t\t\t\t\t<text class=\"movie-year\">{{movie.year}}</text>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</scroll-view>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\ttype Movie = {\n\t\ttitle: string\n\t\tyear: string\n\t\tposter: string\n\t}\n\texport default {\n\t\tdata() {\n\t\t\treturn {\n\t\t\t\tmovies: [\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\ttitle: '阿凡达：水之道',\r\n\t\t\t\t\t\tyear: '2022',\r\n\t\t\t\t\t\tposter: 'https://img.omdbapi.com/?i=tt1630029&h=600&apikey=placeholder'\r\n\t\t\t\t\t},\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\ttitle: '复仇者联盟：终局之战',\r\n\t\t\t\t\t\tyear: '2019',\r\n\t\t\t\t\t\tposter: 'https://img.omdbapi.com/?i=tt4154796&h=600&apikey=placeholder'\r\n\t\t\t\t\t},\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\ttitle: '蜘蛛侠：英雄无归',\r\n\t\t\t\t\t\tyear: '2021',\r\n\t\t\t\t\t\tposter: 'https://img.omdbapi.com/?i=tt10872600&h=600&apikey=placeholder'\r\n\t\t\t\t\t},\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\ttitle: '黑豹',\r\n\t\t\t\t\t\tyear: '2018',\r\n\t\t\t\t\t\tposter: 'https://img.omdbapi.com/?i=tt1825683&h=600&apikey=placeholder'\r\n\t\t\t\t\t},\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\ttitle: '奇异博士2：疯狂多元宇宙',\r\n\t\t\t\t\t\tyear: '2022',\r\n\t\t\t\t\t\tposter: 'https://img.omdbapi.com/?i=tt9419884&h=600&apikey=placeholder'\r\n\t\t\t\t\t},\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\ttitle: '雷神4：爱与雷电',\r\n\t\t\t\t\t\tyear: '2022',\r\n\t\t\t\t\t\tposter: 'https://img.omdbapi.com/?i=tt10648342&h=600&apikey=placeholder'\r\n\t\t\t\t\t}\r\n\t\t\t\t] as Movie[]\r\n\t\t\t}\r\n\t\t},\r\n\t\tonLoad() {\r\n\t\t\t// 页面加载时的初始化逻辑\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\tselectMovie(movie: Movie) {\r\n\t\t\t\tuni.showToast({\r\n\t\t\t\t\ttitle: `选择了: ${movie.title}`,\r\n\t\t\t\t\tduration: 2000\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\tgoToConfig() {\r\n\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\turl: '/pages/config/config'\r\n\t\t\t\t})\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style>\r\n\t.container {\r\n\t\tflex: 1;\r\n\t\tbackground-color: #1a1a1a;\r\n\t\tmin-height: 100vh;\r\n\t}\r\n\r\n\t.header {\r\n\t\tdisplay: flex;\r\n\t\tjustify-content: space-between;\r\n\t\talign-items: center;\r\n\t\tpadding: 20px;\r\n\t\tbackground-color: #2d2d2d;\r\n\t\tborder-bottom: 1px solid #404040;\r\n\t}\r\n\r\n\t.header-title {\r\n\t\tfont-size: 24px;\r\n\t\tfont-weight: bold;\r\n\t\tcolor: #ffffff;\r\n\t}\r\n\r\n\t.header-actions {\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t}\r\n\r\n\t.config-btn {\r\n\t\tbackground-color: #007AFF;\r\n\t\tcolor: #ffffff;\r\n\t\tborder: none;\r\n\t\tborder-radius: 8px;\r\n\t\tpadding: 8px 16px;\r\n\t\tfont-size: 14px;\r\n\t\tcursor: pointer;\r\n\t}\r\n\r\n\t.config-btn:hover {\r\n\t\tbackground-color: #0056CC;\r\n\t}\r\n\r\n\t.movie-grid {\r\n\t\tflex: 1;\r\n\t\tpadding: 20px;\r\n\t}\r\n\r\n\t.grid-container {\r\n\t\tdisplay: flex;\r\n\t\tflex-wrap: wrap;\r\n\t\tgap: 20px;\r\n\t\tjustify-content: space-between;\r\n\t}\r\n\r\n\t.movie-card {\r\n\t\twidth: calc(50% - 10px);\r\n\t\tbackground-color: #2d2d2d;\r\n\t\tborder-radius: 12px;\r\n\t\toverflow: hidden;\r\n\t\tbox-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);\r\n\t\ttransition: transform 0.2s ease;\r\n\t\tcursor: pointer;\r\n\t}\r\n\r\n\t.movie-card:hover {\r\n\t\ttransform: translateY(-4px);\r\n\t\tbox-shadow: 0 8px 16px rgba(0, 0, 0, 0.4);\r\n\t}\r\n\r\n\t.movie-poster {\r\n\t\twidth: 100%;\r\n\t\theight: 240px;\r\n\t\tbackground-color: #404040;\r\n\t}\r\n\r\n\t.movie-info {\r\n\t\tpadding: 12px;\r\n\t}\r\n\r\n\t.movie-title {\r\n\t\tfont-size: 16px;\r\n\t\tfont-weight: bold;\r\n\t\tcolor: #ffffff;\r\n\t\tmargin-bottom: 4px;\r\n\t\tdisplay: block;\r\n\t\toverflow: hidden;\r\n\t\ttext-overflow: ellipsis;\r\n\t\twhite-space: nowrap;\r\n\t}\r\n\r\n\t.movie-year {\r\n\t\tfont-size: 14px;\r\n\t\tcolor: #999999;\r\n\t\tdisplay: block;\r\n\t}\r\n\r\n\t/* 响应式设计 - 大屏幕显示更多列 */\r\n\t@media (min-width: 768px) {\r\n\t\t.movie-card {\r\n\t\t\twidth: calc(33.333% - 14px);\r\n\t\t}\r\n\t}\r\n\r\n\t@media (min-width: 1024px) {\r\n\t\t.movie-card {\r\n\t\t\twidth: calc(25% - 15px);\r\n\t\t}\r\n\t}\r\n</style>\r\n"], "mappings": ";CAmCC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE;EACZ,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;EACZ,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;EACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;CACd;CACA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACd,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;GACN,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;KACP;MACC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAChB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;KACvE,CAAC;KACD;MACC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACnB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;KACvE,CAAC;KACD;MACC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACjB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;KACxE,CAAC;KACD;MACC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACX,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;KACvE,CAAC;KACD;MACC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACrB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;KACvE,CAAC;KACD;MACC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACjB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;KACxE;IACD,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;GACZ;EACD,CAAC;EACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;GACR,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACd,CAAC;EACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;GACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACzB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;KACb,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;KAC5B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACd,CAAC;GACF,CAAC;GACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;KACd,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC;GACF;EACD;CACD;;;;;;SA5FA,IA8BO,cA9BD,KAAK,EAAC,WAAW;IAEtB,IAKO,cALD,KAAK,EAAC,QAAQ;MACnB,IAAqC,cAA/B,KAAK,EAAC,cAAc,KAAC,KAAG;MAC9B,IAEO,cAFD,KAAK,EAAC,gBAAgB;QAC3B,IAA0D;UAAlD,KAAK,EAAC,YAAY;UAAE,OAAK,EAAE,eAAU;YAAE,IAAE;;;IAKnD,IAmBc;MAnBD,KAAK,EAAC,YAAY;MAAC,UAAQ,EAAC,MAAM;;MAC9C,IAiBO,cAjBD,KAAK,EAAC,gBAAgB;QAC3B,IAeO,yCAbmB,WAAM,GAAvB,KAAK,EAAE,KAAK,EAAZ,OAAK;iBAFd,IAeO;YAdN,KAAK,EAAC,YAAY;YAEjB,GAAG,EAAE,KAAK;YACV,OAAK,SAAE,gBAAW,CAAC,KAAK;;YAEzB,IAIE;cAHD,KAAK,EAAC,cAAc;cACnB,GAAG,EAAE,KAAK,CAAC,MAAM;cAClB,IAAI,EAAC,YAAY;;YAElB,IAGO,cAHD,KAAK,EAAC,YAAY;cACvB,IAAgD,cAA1C,KAAK,EAAC,aAAa,SAAG,KAAK,CAAC,KAAK;cACvC,IAA8C,cAAxC,KAAK,EAAC,YAAY,SAAG,KAAK,CAAC,IAAI"}