{"version": 3, "file": "pages/index/index.uvue", "names": [], "sources": ["pages/index/index.uvue"], "sourcesContent": ["<template>\r\n\t<view>\r\n\t\t<image class=\"logo\" src=\"/static/logo.png\"></image>\r\n\t\t<text class=\"title\">{{title}}</text>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\texport default {\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\ttitle: 'Hello'\r\n\t\t\t}\r\n\t\t},\r\n\t\tonLoad() {\r\n\r\n\t\t},\r\n\t\tmethods: {\r\n\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style>\r\n\t.logo {\r\n\t\theight: 100px;\r\n\t\twidth: 100px;\r\n\t\tmargin: 100px auto 25px auto;\r\n\t}\r\n\r\n\t.title {\r\n\t\tfont-size: 18px;\r\n\t\tcolor: #8f8f94;\r\n    text-align: center;\r\n\t}\r\n</style>\r\n"], "mappings": ";CAQC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACd,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;GACN,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACN,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;GACd;EACD,CAAC;EACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;;EAET,CAAC;EACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;;EAET;CACD;;;;;;SAnBA,IAGO;IAFN,IAAmD;MAA5C,KAAK,EAAC,MAAM;MAAC,GAAG,EAAC,kBAAkB;;IAC1C,IAAoC,cAA9B,KAAK,EAAC,OAAO,SAAG,UAAK"}