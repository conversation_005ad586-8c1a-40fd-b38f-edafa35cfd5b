<template>
	<view class="container">
		<!-- 顶部导航栏 -->
		<view class="header">
			<button class="back-btn" @click="goBack">返回</button>
			<text class="header-title">WebDAV配置</text>
			<view class="header-placeholder"></view>
		</view>
		
		<!-- 配置表单 -->
		<scroll-view class="config-content" scroll-y="true">
			<view class="form-container">
				<view class="form-section">
					<text class="section-title">服务器设置</text>
					
					<view class="form-item">
						<text class="form-label">服务器地址</text>
						<input 
							class="form-input" 
							v-model="config['serverUrl']" 
							placeholder="https://your-webdav-server.com"
							type="url"
						/>
					</view>
					
					<view class="form-item">
						<text class="form-label">端口号</text>
						<input 
							class="form-input" 
							v-model="config['port']" 
							placeholder="443"
							type="number"
						/>
					</view>
				</view>
				
				<view class="form-section">
					<text class="section-title">认证信息</text>
					
					<view class="form-item">
						<text class="form-label">用户名</text>
						<input 
							class="form-input" 
							v-model="config['username']" 
							placeholder="请输入用户名"
							type="text"
						/>
					</view>
					
					<view class="form-item">
						<text class="form-label">密码</text>
						<input 
							class="form-input" 
							v-model="config['password']" 
							placeholder="请输入密码"
							type="password"
						/>
					</view>
				</view>
				
				<view class="form-section">
					<text class="section-title">高级设置</text>
					
					<view class="form-item">
						<text class="form-label">根目录</text>
						<input 
							class="form-input" 
							v-model="config['rootPath']" 
							placeholder="/movies"
							type="text"
						/>
					</view>
					
					<view class="form-item">
						<view class="form-row">
							<text class="form-label">启用HTTPS</text>
							<switch 
								class="form-switch" 
							:checked="config['useHttps']"
							/>
						</view>
					</view>
					
					<view class="form-item">
						<view class="form-row">
							<text class="form-label">自动连接</text>
							<switch 
								class="form-switch" 
							:checked="config['autoConnect']" 
							/>
						</view>
					</view>
				</view>
				
				<!-- 操作按钮 -->
				<view class="button-group">
					<button class="test-btn" @click="testConnection">测试连接</button>
					<button class="save-btn" @click="saveConfig">保存配置</button>
				</view>
				
				<!-- 连接状态 -->
				<view class="status-section" v-if="connectionStatus">
					<text class="status-text" :class="statusClass">{{connectionStatus}}</text>
				</view>
			</view>
		</scroll-view>
	</view>
</template>

<script>
	type Config = {
		serverUrl: string
		port: string
		username: string
		password: string
		rootPath: string
		useHttps: boolean
		autoConnect: boolean
	}
	export default {
		data() {
			return {
				config: {
					serverUrl: '',
					port: '443',
					username: '',
					password: '',
					rootPath: '/movies',
					useHttps: true,
					autoConnect: false
				},
				connectionStatus: '',
				statusClass: ''
			}
		},
		onLoad() {
			this.loadConfig()
		},
		methods: {
			goBack() {
				uni.navigateBack()
			},
			
			loadConfig() {
				// 从本地存储加载配置
				try {
					const savedConfig = uni.getStorageSync('webdav_config')
					if (savedConfig != null) {
						const configData = savedConfig as UTSJSONObject
						this.config['serverUrl'] = configData['serverUrl'] as string ?? ''
						this.config['port'] = configData['port'] as string ?? '443'
						this.config['username'] = configData['username'] as string ?? ''
						this.config['password'] = configData['password'] as string ?? ''
						this.config['rootPath'] = configData['rootPath'] as string ?? '/movies'
						this.config['useHttps'] = configData['useHttps'] as boolean ?? true
						this.config['autoConnect'] = configData['autoConnect'] as boolean ?? false
					}
				} catch (e) {
					console.error(e as any)
				}
			},
			
			saveConfig() {
				// 验证必填字段
			if (this.config['serverUrl'] == null || (this.config['serverUrl'] as string).trim() == '') {
					uni.showToast({
						title: '请输入服务器地址',
						icon: 'none'
					})
					return
				}
				
			if (this.config['username'] == null || (this.config['username'] as string).trim() == '') {
					uni.showToast({
						title: '请输入用户名',
						icon: 'none'
					})
					return
				}
				
				// 保存到本地存储
				try {
					uni.setStorageSync('webdav_config', this.config)
					uni.showToast({
						title: '配置保存成功',
						icon: 'success'
					})
				} catch (e) {
					uni.showToast({
						title: '保存失败',
						icon: 'error'
					})
					console.error(e as any)
				}
			},
			
			async testConnection() {
			if ((this.config['serverUrl'] == null || (this.config['serverUrl'] as string).trim() == '') || 
				(this.config['username'] == null || (this.config['username'] as string).trim() == '')) {
					uni.showToast({
						title: '请先填写服务器地址和用户名',
						icon: 'none'
					})
					return
				}
				
				this.connectionStatus = '正在测试连接...'
				this.statusClass = 'status-testing'
			}
		}
	}
</script>

<style>
	.container {
		flex: 1;
		background-color: #1a1a1a;
		min-height: 100vh;
	}

	.header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 20px;
		background-color: #2d2d2d;
		border-bottom: 1px solid #404040;
	}

	.back-btn {
		background-color: #404040;
		color: #ffffff;
		border: none;
		border-radius: 8px;
		padding: 8px 16px;
		font-size: 14px;
		cursor: pointer;
	}

	.back-btn:hover {
		background-color: #505050;
	}

	.header-title {
		font-size: 20px;
		font-weight: bold;
		color: #ffffff;
	}

	.header-placeholder {
		width: 60px;
	}

	.config-content {
		flex: 1;
		padding: 20px;
	}

	.form-container {
		max-width: 600px;
		margin: 0 auto;
	}

	.form-section {
		margin-bottom: 30px;
		background-color: #2d2d2d;
		border-radius: 12px;
		padding: 20px;
	}

	.section-title {
		font-size: 18px;
		font-weight: bold;
		color: #ffffff;
		margin-bottom: 20px;
		display: block;
	}

	.form-item {
		margin-bottom: 20px;
	}

	.form-item:last-child {
		margin-bottom: 0;
	}

	.form-label {
		font-size: 16px;
		color: #cccccc;
		margin-bottom: 8px;
		display: block;
	}

	.form-input {
		width: 100%;
		padding: 12px 16px;
		background-color: #404040;
		border: 1px solid #555555;
		border-radius: 8px;
		color: #ffffff;
		font-size: 16px;
	}

	.form-input:focus {
		border-color: #007AFF;
		outline: none;
	}

	.form-row {
		display: flex;
		justify-content: space-between;
		align-items: center;
	}

	.form-switch {
		transform: scale(1.2);
	}

	.button-group {
		display: flex;
		gap: 15px;
		margin-top: 30px;
	}

	.test-btn, .save-btn {
		flex: 1;
		padding: 15px;
		border: none;
		border-radius: 8px;
		font-size: 16px;
		font-weight: bold;
		cursor: pointer;
	}

	.test-btn {
		background-color: #FF9500;
		color: #ffffff;
	}

	.test-btn:hover {
		background-color: #E6850E;
	}

	.save-btn {
		background-color: #007AFF;
		color: #ffffff;
	}

	.save-btn:hover {
		background-color: #0056CC;
	}

	.status-section {
		margin-top: 20px;
		text-align: center;
	}

	.status-text {
		font-size: 16px;
		font-weight: bold;
		padding: 12px 20px;
		border-radius: 8px;
		display: inline-block;
	}

	.status-testing {
		background-color: #FF9500;
		color: #ffffff;
	}

	.status-success {
		background-color: #34C759;
		color: #ffffff;
	}

	.status-error {
		background-color: #FF3B30;
		color: #ffffff;
	}
</style>
