@file:Suppress("UNCHECKED_CAST", "USELESS_CAST", "INAPPLICABLE_JVM_NAME", "UNUSED_ANONYMOUS_PARAMETER", "NAME_SHADOWING", "UNNECESSARY_NOT_NULL_ASSERTION")
package uni.UNI666077E
import io.dcloud.uniapp.*
import io.dcloud.uniapp.extapi.*
import io.dcloud.uniapp.framework.*
import io.dcloud.uniapp.runtime.*
import io.dcloud.uniapp.vue.*
import io.dcloud.uniapp.vue.shared.*
import io.dcloud.unicloud.*
import io.dcloud.uts.*
import io.dcloud.uts.Map
import io.dcloud.uts.Set
import io.dcloud.uts.UTSAndroid
import io.dcloud.uniapp.extapi.navigateTo as uni_navigateTo
import io.dcloud.uniapp.extapi.showToast as uni_showToast
open class GenPagesIndexIndex : BasePage {
    constructor(__ins: ComponentInternalInstance, __renderer: String?) : super(__ins, __renderer) {
        onLoad(fun(_: OnLoadOptions) {}, __ins)
    }
    @Suppress("UNUSED_PARAMETER", "UNUSED_VARIABLE")
    override fun `$render`(): Any? {
        val _ctx = this
        val _cache = this.`$`.renderCache
        return _cE("view", _uM("class" to "container"), _uA(
            _cE("view", _uM("class" to "header"), _uA(
                _cE("text", _uM("class" to "header-title"), "电影库"),
                _cE("view", _uM("class" to "header-actions"), _uA(
                    _cE("button", _uM("class" to "config-btn", "onClick" to _ctx.goToConfig), "设置", 8, _uA(
                        "onClick"
                    ))
                ))
            )),
            _cE("scroll-view", _uM("class" to "movie-grid", "scroll-y" to "true"), _uA(
                _cE("view", _uM("class" to "grid-container"), _uA(
                    _cE(Fragment, null, RenderHelpers.renderList(_ctx.movies, fun(movie, index, __index, _cached): Any {
                        return _cE("view", _uM("class" to "movie-card", "key" to index, "onClick" to fun(){
                            _ctx.selectMovie(movie)
                        }
                        ), _uA(
                            _cE("image", _uM("class" to "movie-poster", "src" to movie.poster, "mode" to "aspectFill"), null, 8, _uA(
                                "src"
                            )),
                            _cE("view", _uM("class" to "movie-info"), _uA(
                                _cE("text", _uM("class" to "movie-title"), _tD(movie.title), 1),
                                _cE("text", _uM("class" to "movie-year"), _tD(movie.year), 1)
                            ))
                        ), 8, _uA(
                            "onClick"
                        ))
                    }
                    ), 128)
                ))
            ))
        ))
    }
    open var movies: UTSArray<Movie> by `$data`
    @Suppress("USELESS_CAST")
    override fun data(): Map<String, Any?> {
        return _uM("movies" to _uA<Movie>(Movie(title = "阿凡达：水之道", year = "2022", poster = "https://img.omdbapi.com/?i=tt1630029&h=600&apikey=placeholder"), Movie(title = "复仇者联盟：终局之战", year = "2019", poster = "https://img.omdbapi.com/?i=tt4154796&h=600&apikey=placeholder"), Movie(title = "蜘蛛侠：英雄无归", year = "2021", poster = "https://img.omdbapi.com/?i=tt10872600&h=600&apikey=placeholder"), Movie(title = "黑豹", year = "2018", poster = "https://img.omdbapi.com/?i=tt1825683&h=600&apikey=placeholder"), Movie(title = "奇异博士2：疯狂多元宇宙", year = "2022", poster = "https://img.omdbapi.com/?i=tt9419884&h=600&apikey=placeholder"), Movie(title = "雷神4：爱与雷电", year = "2022", poster = "https://img.omdbapi.com/?i=tt10648342&h=600&apikey=placeholder")))
    }
    open var selectMovie = ::gen_selectMovie_fn
    open fun gen_selectMovie_fn(movie: Movie) {
        uni_showToast(ShowToastOptions(title = "\u9009\u62E9\u4E86: " + movie.title, duration = 2000))
    }
    open var goToConfig = ::gen_goToConfig_fn
    open fun gen_goToConfig_fn() {
        uni_navigateTo(NavigateToOptions(url = "/pages/config/config"))
    }
    companion object {
        val styles: Map<String, Map<String, Map<String, Any>>> by lazy {
            _nCS(_uA(
                styles0
            ), _uA(
                GenApp.styles
            ))
        }
        val styles0: Map<String, Map<String, Map<String, Any>>>
            get() {
                return _uM("container" to _pS(_uM("flex" to 1, "backgroundColor" to "#1a1a1a")), "header" to _pS(_uM("display" to "flex", "justifyContent" to "space-between", "alignItems" to "center", "paddingTop" to 20, "paddingRight" to 20, "paddingBottom" to 20, "paddingLeft" to 20, "backgroundColor" to "#2d2d2d", "borderBottomWidth" to 1, "borderBottomStyle" to "solid", "borderBottomColor" to "#404040")), "header-title" to _pS(_uM("fontSize" to 24, "fontWeight" to "bold", "color" to "#ffffff")), "header-actions" to _pS(_uM("display" to "flex", "alignItems" to "center")), "config-btn" to _pS(_uM("backgroundColor" to "#007AFF", "color" to "#ffffff", "borderTopWidth" to "medium", "borderRightWidth" to "medium", "borderBottomWidth" to "medium", "borderLeftWidth" to "medium", "borderTopStyle" to "none", "borderRightStyle" to "none", "borderBottomStyle" to "none", "borderLeftStyle" to "none", "borderTopColor" to "#000000", "borderRightColor" to "#000000", "borderBottomColor" to "#000000", "borderLeftColor" to "#000000", "borderTopLeftRadius" to 8, "borderTopRightRadius" to 8, "borderBottomRightRadius" to 8, "borderBottomLeftRadius" to 8, "paddingTop" to 8, "paddingRight" to 16, "paddingBottom" to 8, "paddingLeft" to 16, "fontSize" to 14, "cursor" to "pointer", "backgroundColor:hover" to "#0056CC")), "movie-grid" to _pS(_uM("flex" to 1, "paddingTop" to 20, "paddingRight" to 20, "paddingBottom" to 20, "paddingLeft" to 20)), "grid-container" to _pS(_uM("display" to "flex", "flexWrap" to "wrap", "gap" to "20px", "justifyContent" to "space-between")), "movie-card" to _pS(_uM("backgroundColor" to "#2d2d2d", "borderTopLeftRadius" to 12, "borderTopRightRadius" to 12, "borderBottomRightRadius" to 12, "borderBottomLeftRadius" to 12, "overflow" to "hidden", "boxShadow" to "0 4px 8px rgba(0, 0, 0, 0.3)", "transitionProperty" to "transform", "transitionDuration" to "0.2s", "transitionTimingFunction" to "ease", "cursor" to "pointer", "transform:hover" to "translateY(-4px)", "boxShadow:hover" to "0 8px 16px rgba(0, 0, 0, 0.4)")), "movie-poster" to _pS(_uM("width" to "100%", "height" to 240, "backgroundColor" to "#404040")), "movie-info" to _pS(_uM("paddingTop" to 12, "paddingRight" to 12, "paddingBottom" to 12, "paddingLeft" to 12)), "movie-title" to _pS(_uM("fontSize" to 16, "fontWeight" to "bold", "color" to "#ffffff", "marginBottom" to 4, "overflow" to "hidden", "textOverflow" to "ellipsis", "whiteSpace" to "nowrap")), "movie-year" to _pS(_uM("fontSize" to 14, "color" to "#999999")), "@FONT-FACE" to _uM("0" to _uM(), "1" to _uM()), "@TRANSITION" to _uM("movie-card" to _uM("property" to "transform", "duration" to "0.2s", "timingFunction" to "ease")))
            }
        var inheritAttrs = true
        var inject: Map<String, Map<String, Any?>> = _uM()
        var emits: Map<String, Any?> = _uM()
        var props = _nP(_uM())
        var propsNeedCastKeys: UTSArray<String> = _uA()
        var components: Map<String, CreateVueComponent> = _uM()
    }
}
