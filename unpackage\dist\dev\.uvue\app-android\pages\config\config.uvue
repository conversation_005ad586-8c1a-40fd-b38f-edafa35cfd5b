type Config = {
    __$originalPosition?: UTSSourceMapPosition<"Config", "pages/config/config.uvue", 111, 7>;
    serverUrl: string;
    port: string;
    username: string;
    password: string;
    rootPath: string;
    useHttps: boolean;
    autoConnect: boolean;
};
const __sfc__ = defineComponent({
    data() {
        return {
            config: {
                serverUrl: '',
                port: '443',
                username: '',
                password: '',
                rootPath: '/movies',
                useHttps: true,
                autoConnect: false
            },
            connectionStatus: '',
            statusClass: ''
        };
    },
    onLoad() {
        this.loadConfig();
    },
    methods: {
        goBack() {
            uni.navigateBack();
        },
        loadConfig() {
            // 从本地存储加载配置
            try {
                const savedConfig = uni.getStorageSync('webdav_config');
                if (savedConfig != null) {
                    const configData = savedConfig as UTSJSONObject;
                    this.config['serverUrl'] = configData['serverUrl'] as string ?? '';
                    this.config['port'] = configData['port'] as string ?? '443';
                    this.config['username'] = configData['username'] as string ?? '';
                    this.config['password'] = configData['password'] as string ?? '';
                    this.config['rootPath'] = configData['rootPath'] as string ?? '/movies';
                    this.config['useHttps'] = configData['useHttps'] as boolean ?? true;
                    this.config['autoConnect'] = configData['autoConnect'] as boolean ?? false;
                }
            }
            catch (e: any) {
                console.error(e as any, " at pages/config/config.uvue:159");
            }
        },
        saveConfig() {
            // 验证必填字段
            if (this.config['serverUrl'] == null || (this.config['serverUrl'] as string).trim() == '') {
                uni.showToast({
                    title: '请输入服务器地址',
                    icon: 'none'
                });
                return;
            }
            if (this.config['username'] == null || (this.config['username'] as string).trim() == '') {
                uni.showToast({
                    title: '请输入用户名',
                    icon: 'none'
                });
                return;
            }
            // 保存到本地存储
            try {
                uni.setStorageSync('webdav_config', this.config);
                uni.showToast({
                    title: '配置保存成功',
                    icon: 'success'
                });
            }
            catch (e: any) {
                uni.showToast({
                    title: '保存失败',
                    icon: 'error'
                });
                console.error(e as any, " at pages/config/config.uvue:193");
            }
        },
        async testConnection(): Promise<void> {
            if ((this.config['serverUrl'] == null || (this.config['serverUrl'] as string).trim() == '') ||
                (this.config['username'] == null || (this.config['username'] as string).trim() == '')) {
                uni.showToast({
                    title: '请先填写服务器地址和用户名',
                    icon: 'none'
                });
                return;
            }
            this.connectionStatus = '正在测试连接...';
            this.statusClass = 'status-testing';
        }
    }
});
export default __sfc__;
function GenPagesConfigConfigRender(this: InstanceType<typeof __sfc__>): any | null {
    const _ctx = this;
    const _cache = this.$.renderCache;
    const _component_switch = resolveComponent("switch");
    return _cE("view", _uM({ class: "container" }), [
        _cE("view", _uM({ class: "header" }), [
            _cE("button", _uM({
                class: "back-btn",
                onClick: _ctx.goBack
            }), "返回", 8 /* PROPS */, ["onClick"]),
            _cE("text", _uM({ class: "header-title" }), "WebDAV配置"),
            _cE("view", _uM({ class: "header-placeholder" }))
        ]),
        _cE("scroll-view", _uM({
            class: "config-content",
            "scroll-y": "true"
        }), [
            _cE("view", _uM({ class: "form-container" }), [
                _cE("view", _uM({ class: "form-section" }), [
                    _cE("text", _uM({ class: "section-title" }), "服务器设置"),
                    _cE("view", _uM({ class: "form-item" }), [
                        _cE("text", _uM({ class: "form-label" }), "服务器地址"),
                        _cE("input", _uM({
                            class: "form-input",
                            modelValue: _ctx.config['serverUrl'],
                            onInput: ($event: UniInputEvent) => { (_ctx.config['serverUrl']) = $event.detail.value; },
                            placeholder: "https://your-webdav-server.com",
                            type: "url"
                        }), null, 40 /* PROPS, NEED_HYDRATION */, ["modelValue", "onInput"])
                    ]),
                    _cE("view", _uM({ class: "form-item" }), [
                        _cE("text", _uM({ class: "form-label" }), "端口号"),
                        _cE("input", _uM({
                            class: "form-input",
                            modelValue: _ctx.config['port'],
                            onInput: ($event: UniInputEvent) => { (_ctx.config['port']) = $event.detail.value; },
                            placeholder: "443",
                            type: "number"
                        }), null, 40 /* PROPS, NEED_HYDRATION */, ["modelValue", "onInput"])
                    ])
                ]),
                _cE("view", _uM({ class: "form-section" }), [
                    _cE("text", _uM({ class: "section-title" }), "认证信息"),
                    _cE("view", _uM({ class: "form-item" }), [
                        _cE("text", _uM({ class: "form-label" }), "用户名"),
                        _cE("input", _uM({
                            class: "form-input",
                            modelValue: _ctx.config['username'],
                            onInput: ($event: UniInputEvent) => { (_ctx.config['username']) = $event.detail.value; },
                            placeholder: "请输入用户名",
                            type: "text"
                        }), null, 40 /* PROPS, NEED_HYDRATION */, ["modelValue", "onInput"])
                    ]),
                    _cE("view", _uM({ class: "form-item" }), [
                        _cE("text", _uM({ class: "form-label" }), "密码"),
                        _cE("input", _uM({
                            class: "form-input",
                            modelValue: _ctx.config['password'],
                            onInput: ($event: UniInputEvent) => { (_ctx.config['password']) = $event.detail.value; },
                            placeholder: "请输入密码",
                            type: "password"
                        }), null, 40 /* PROPS, NEED_HYDRATION */, ["modelValue", "onInput"])
                    ])
                ]),
                _cE("view", _uM({ class: "form-section" }), [
                    _cE("text", _uM({ class: "section-title" }), "高级设置"),
                    _cE("view", _uM({ class: "form-item" }), [
                        _cE("text", _uM({ class: "form-label" }), "根目录"),
                        _cE("input", _uM({
                            class: "form-input",
                            modelValue: _ctx.config['rootPath'],
                            onInput: ($event: UniInputEvent) => { (_ctx.config['rootPath']) = $event.detail.value; },
                            placeholder: "/movies",
                            type: "text"
                        }), null, 40 /* PROPS, NEED_HYDRATION */, ["modelValue", "onInput"])
                    ]),
                    _cE("view", _uM({ class: "form-item" }), [
                        _cE("view", _uM({ class: "form-row" }), [
                            _cE("text", _uM({ class: "form-label" }), "启用HTTPS"),
                            _cV(_component_switch, _uM({
                                class: "form-switch",
                                checked: _ctx.config['useHttps']
                            }), null, 8 /* PROPS */, ["checked"])
                        ])
                    ]),
                    _cE("view", _uM({ class: "form-item" }), [
                        _cE("view", _uM({ class: "form-row" }), [
                            _cE("text", _uM({ class: "form-label" }), "自动连接"),
                            _cV(_component_switch, _uM({
                                class: "form-switch",
                                checked: _ctx.config['autoConnect']
                            }), null, 8 /* PROPS */, ["checked"])
                        ])
                    ])
                ]),
                _cE("view", _uM({ class: "button-group" }), [
                    _cE("button", _uM({
                        class: "test-btn",
                        onClick: _ctx.testConnection
                    }), "测试连接", 8 /* PROPS */, ["onClick"]),
                    _cE("button", _uM({
                        class: "save-btn",
                        onClick: _ctx.saveConfig
                    }), "保存配置", 8 /* PROPS */, ["onClick"])
                ]),
                isTrue(_ctx.connectionStatus)
                    ? _cE("view", _uM({
                        key: 0,
                        class: "status-section"
                    }), [
                        _cE("text", _uM({
                            class: _nC(["status-text", _ctx.statusClass])
                        }), _tD(_ctx.connectionStatus), 3 /* TEXT, CLASS */)
                    ])
                    : _cC("v-if", true)
            ])
        ])
    ]);
}
const GenPagesConfigConfigStyles = [_uM([["container", _pS(_uM([["flex", 1], ["backgroundColor", "#1a1a1a"]]))], ["header", _pS(_uM([["display", "flex"], ["justifyContent", "space-between"], ["alignItems", "center"], ["paddingTop", 20], ["paddingRight", 20], ["paddingBottom", 20], ["paddingLeft", 20], ["backgroundColor", "#2d2d2d"], ["borderBottomWidth", 1], ["borderBottomStyle", "solid"], ["borderBottomColor", "#404040"]]))], ["back-btn", _pS(_uM([["backgroundColor", "#404040"], ["color", "#ffffff"], ["borderTopWidth", "medium"], ["borderRightWidth", "medium"], ["borderBottomWidth", "medium"], ["borderLeftWidth", "medium"], ["borderTopStyle", "none"], ["borderRightStyle", "none"], ["borderBottomStyle", "none"], ["borderLeftStyle", "none"], ["borderTopColor", "#000000"], ["borderRightColor", "#000000"], ["borderBottomColor", "#000000"], ["borderLeftColor", "#000000"], ["borderTopLeftRadius", 8], ["borderTopRightRadius", 8], ["borderBottomRightRadius", 8], ["borderBottomLeftRadius", 8], ["paddingTop", 8], ["paddingRight", 16], ["paddingBottom", 8], ["paddingLeft", 16], ["fontSize", 14], ["cursor", "pointer"], ["backgroundColor:hover", "#505050"]]))], ["header-title", _pS(_uM([["fontSize", 20], ["fontWeight", "bold"], ["color", "#ffffff"]]))], ["header-placeholder", _pS(_uM([["width", 60]]))], ["config-content", _pS(_uM([["flex", 1], ["paddingTop", 20], ["paddingRight", 20], ["paddingBottom", 20], ["paddingLeft", 20]]))], ["form-container", _pS(_uM([["maxWidth", 600], ["marginTop", 0], ["marginRight", "auto"], ["marginBottom", 0], ["marginLeft", "auto"]]))], ["form-section", _pS(_uM([["marginBottom", 30], ["backgroundColor", "#2d2d2d"], ["borderTopLeftRadius", 12], ["borderTopRightRadius", 12], ["borderBottomRightRadius", 12], ["borderBottomLeftRadius", 12], ["paddingTop", 20], ["paddingRight", 20], ["paddingBottom", 20], ["paddingLeft", 20]]))], ["section-title", _pS(_uM([["fontSize", 18], ["fontWeight", "bold"], ["color", "#ffffff"], ["marginBottom", 20]]))], ["form-item", _pS(_uM([["marginBottom", 20], ["marginBottom:last-child", 0]]))], ["form-label", _pS(_uM([["fontSize", 16], ["color", "#cccccc"], ["marginBottom", 8]]))], ["form-input", _pS(_uM([["width", "100%"], ["paddingTop", 12], ["paddingRight", 16], ["paddingBottom", 12], ["paddingLeft", 16], ["backgroundColor", "#404040"], ["borderTopWidth", 1], ["borderRightWidth", 1], ["borderBottomWidth", 1], ["borderLeftWidth", 1], ["borderTopStyle", "solid"], ["borderRightStyle", "solid"], ["borderBottomStyle", "solid"], ["borderLeftStyle", "solid"], ["borderTopColor", "#555555"], ["borderRightColor", "#555555"], ["borderBottomColor", "#555555"], ["borderLeftColor", "#555555"], ["borderTopLeftRadius", 8], ["borderTopRightRadius", 8], ["borderBottomRightRadius", 8], ["borderBottomLeftRadius", 8], ["color", "#ffffff"], ["fontSize", 16], ["borderTopColor:focus", "#007AFF"], ["borderRightColor:focus", "#007AFF"], ["borderBottomColor:focus", "#007AFF"], ["borderLeftColor:focus", "#007AFF"], ["outline:focus", "none"]]))], ["form-row", _pS(_uM([["display", "flex"], ["justifyContent", "space-between"], ["alignItems", "center"]]))], ["form-switch", _pS(_uM([["transform", "scale(1.2)"]]))], ["button-group", _pS(_uM([["display", "flex"], ["gap", "15px"], ["marginTop", 30]]))], ["test-btn", _pS(_uM([["flex", 1], ["paddingTop", 15], ["paddingRight", 15], ["paddingBottom", 15], ["paddingLeft", 15], ["borderTopWidth", "medium"], ["borderRightWidth", "medium"], ["borderBottomWidth", "medium"], ["borderLeftWidth", "medium"], ["borderTopStyle", "none"], ["borderRightStyle", "none"], ["borderBottomStyle", "none"], ["borderLeftStyle", "none"], ["borderTopColor", "#000000"], ["borderRightColor", "#000000"], ["borderBottomColor", "#000000"], ["borderLeftColor", "#000000"], ["borderTopLeftRadius", 8], ["borderTopRightRadius", 8], ["borderBottomRightRadius", 8], ["borderBottomLeftRadius", 8], ["fontSize", 16], ["fontWeight", "bold"], ["cursor", "pointer"], ["backgroundColor", "#FF9500"], ["color", "#ffffff"], ["backgroundColor:hover", "#E6850E"]]))], ["save-btn", _pS(_uM([["flex", 1], ["paddingTop", 15], ["paddingRight", 15], ["paddingBottom", 15], ["paddingLeft", 15], ["borderTopWidth", "medium"], ["borderRightWidth", "medium"], ["borderBottomWidth", "medium"], ["borderLeftWidth", "medium"], ["borderTopStyle", "none"], ["borderRightStyle", "none"], ["borderBottomStyle", "none"], ["borderLeftStyle", "none"], ["borderTopColor", "#000000"], ["borderRightColor", "#000000"], ["borderBottomColor", "#000000"], ["borderLeftColor", "#000000"], ["borderTopLeftRadius", 8], ["borderTopRightRadius", 8], ["borderBottomRightRadius", 8], ["borderBottomLeftRadius", 8], ["fontSize", 16], ["fontWeight", "bold"], ["cursor", "pointer"], ["backgroundColor", "#007AFF"], ["color", "#ffffff"], ["backgroundColor:hover", "#0056CC"]]))], ["status-section", _pS(_uM([["marginTop", 20], ["textAlign", "center"]]))], ["status-text", _pS(_uM([["fontSize", 16], ["fontWeight", "bold"], ["paddingTop", 12], ["paddingRight", 20], ["paddingBottom", 12], ["paddingLeft", 20], ["borderTopLeftRadius", 8], ["borderTopRightRadius", 8], ["borderBottomRightRadius", 8], ["borderBottomLeftRadius", 8]]))], ["status-testing", _pS(_uM([["backgroundColor", "#FF9500"], ["color", "#ffffff"]]))], ["status-success", _pS(_uM([["backgroundColor", "#34C759"], ["color", "#ffffff"]]))], ["status-error", _pS(_uM([["backgroundColor", "#FF3B30"], ["color", "#ffffff"]]))]])];
//# sourceMappingURL=config.uvue.map