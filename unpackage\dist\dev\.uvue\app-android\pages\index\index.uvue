type Movie = {
    __$originalPosition?: UTSSourceMapPosition<"Movie", "pages/index/index.uvue", 36, 7>;
    title: string;
    year: string;
    poster: string;
};
const __sfc__ = defineComponent({
    data() {
        return {
            movies: [
                {
                    title: '阿凡达：水之道',
                    year: '2022',
                    poster: 'https://img.omdbapi.com/?i=tt1630029&h=600&apikey=placeholder'
                } as Movie,
                {
                    title: '复仇者联盟：终局之战',
                    year: '2019',
                    poster: 'https://img.omdbapi.com/?i=tt4154796&h=600&apikey=placeholder'
                } as Movie,
                {
                    title: '蜘蛛侠：英雄无归',
                    year: '2021',
                    poster: 'https://img.omdbapi.com/?i=tt10872600&h=600&apikey=placeholder'
                } as Movie,
                {
                    title: '黑豹',
                    year: '2018',
                    poster: 'https://img.omdbapi.com/?i=tt1825683&h=600&apikey=placeholder'
                } as Movie,
                {
                    title: '奇异博士2：疯狂多元宇宙',
                    year: '2022',
                    poster: 'https://img.omdbapi.com/?i=tt9419884&h=600&apikey=placeholder'
                } as Movie,
                {
                    title: '雷神4：爱与雷电',
                    year: '2022',
                    poster: 'https://img.omdbapi.com/?i=tt10648342&h=600&apikey=placeholder'
                } as Movie
            ] as Movie[]
        };
    },
    onLoad() {
        // 页面加载时的初始化逻辑
    },
    methods: {
        selectMovie(movie: Movie) {
            uni.showToast({
                title: `选择了: ${movie.title}`,
                duration: 2000
            });
        },
        goToConfig() {
            uni.navigateTo({
                url: '/pages/config/config'
            });
        }
    }
});
export default __sfc__;
function GenPagesIndexIndexRender(this: InstanceType<typeof __sfc__>): any | null {
    const _ctx = this;
    const _cache = this.$.renderCache;
    return _cE("view", _uM({ class: "container" }), [
        _cE("view", _uM({ class: "header" }), [
            _cE("text", _uM({ class: "header-title" }), "电影库"),
            _cE("view", _uM({ class: "header-actions" }), [
                _cE("button", _uM({
                    class: "config-btn",
                    onClick: _ctx.goToConfig
                }), "设置", 8 /* PROPS */, ["onClick"])
            ])
        ]),
        _cE("scroll-view", _uM({
            class: "movie-grid",
            "scroll-y": "true"
        }), [
            _cE("view", _uM({ class: "grid-container" }), [
                _cE(Fragment, null, RenderHelpers.renderList(_ctx.movies, (movie, index, __index, _cached): any => {
                    return _cE("view", _uM({
                        class: "movie-card",
                        key: index,
                        onClick: () => { _ctx.selectMovie(movie); }
                    }), [
                        _cE("image", _uM({
                            class: "movie-poster",
                            src: movie.poster,
                            mode: "aspectFill"
                        }), null, 8 /* PROPS */, ["src"]),
                        _cE("view", _uM({ class: "movie-info" }), [
                            _cE("text", _uM({ class: "movie-title" }), _tD(movie.title), 1 /* TEXT */),
                            _cE("text", _uM({ class: "movie-year" }), _tD(movie.year), 1 /* TEXT */)
                        ])
                    ], 8 /* PROPS */, ["onClick"]);
                }), 128 /* KEYED_FRAGMENT */)
            ])
        ])
    ]);
}
const GenPagesIndexIndexStyles = [_uM([["container", _pS(_uM([["flex", 1], ["backgroundColor", "#1a1a1a"]]))], ["header", _pS(_uM([["display", "flex"], ["justifyContent", "space-between"], ["alignItems", "center"], ["paddingTop", 20], ["paddingRight", 20], ["paddingBottom", 20], ["paddingLeft", 20], ["backgroundColor", "#2d2d2d"], ["borderBottomWidth", 1], ["borderBottomStyle", "solid"], ["borderBottomColor", "#404040"]]))], ["header-title", _pS(_uM([["fontSize", 24], ["fontWeight", "bold"], ["color", "#ffffff"]]))], ["header-actions", _pS(_uM([["display", "flex"], ["alignItems", "center"]]))], ["config-btn", _pS(_uM([["backgroundColor", "#007AFF"], ["color", "#ffffff"], ["borderTopWidth", "medium"], ["borderRightWidth", "medium"], ["borderBottomWidth", "medium"], ["borderLeftWidth", "medium"], ["borderTopStyle", "none"], ["borderRightStyle", "none"], ["borderBottomStyle", "none"], ["borderLeftStyle", "none"], ["borderTopColor", "#000000"], ["borderRightColor", "#000000"], ["borderBottomColor", "#000000"], ["borderLeftColor", "#000000"], ["borderTopLeftRadius", 8], ["borderTopRightRadius", 8], ["borderBottomRightRadius", 8], ["borderBottomLeftRadius", 8], ["paddingTop", 8], ["paddingRight", 16], ["paddingBottom", 8], ["paddingLeft", 16], ["fontSize", 14], ["cursor", "pointer"], ["backgroundColor:hover", "#0056CC"]]))], ["movie-grid", _pS(_uM([["flex", 1], ["paddingTop", 20], ["paddingRight", 20], ["paddingBottom", 20], ["paddingLeft", 20]]))], ["grid-container", _pS(_uM([["display", "flex"], ["flexWrap", "wrap"], ["gap", "20px"], ["justifyContent", "space-between"]]))], ["movie-card", _pS(_uM([["backgroundColor", "#2d2d2d"], ["borderTopLeftRadius", 12], ["borderTopRightRadius", 12], ["borderBottomRightRadius", 12], ["borderBottomLeftRadius", 12], ["overflow", "hidden"], ["boxShadow", "0 4px 8px rgba(0, 0, 0, 0.3)"], ["transitionProperty", "transform"], ["transitionDuration", "0.2s"], ["transitionTimingFunction", "ease"], ["cursor", "pointer"], ["transform:hover", "translateY(-4px)"], ["boxShadow:hover", "0 8px 16px rgba(0, 0, 0, 0.4)"]]))], ["movie-poster", _pS(_uM([["width", "100%"], ["height", 240], ["backgroundColor", "#404040"]]))], ["movie-info", _pS(_uM([["paddingTop", 12], ["paddingRight", 12], ["paddingBottom", 12], ["paddingLeft", 12]]))], ["movie-title", _pS(_uM([["fontSize", 16], ["fontWeight", "bold"], ["color", "#ffffff"], ["marginBottom", 4], ["overflow", "hidden"], ["textOverflow", "ellipsis"], ["whiteSpace", "nowrap"]]))], ["movie-year", _pS(_uM([["fontSize", 14], ["color", "#999999"]]))], ["@FONT-FACE", _uM([["0", _uM([])], ["1", _uM([])]])], ["@TRANSITION", _uM([["movie-card", _uM([["property", "transform"], ["duration", "0.2s"], ["timingFunction", "ease"]])]])]])];
//# sourceMappingURL=index.uvue.map