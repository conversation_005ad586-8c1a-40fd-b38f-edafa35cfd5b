{"version": 3, "sources": ["pages/config/config.uvue", "App.uvue"], "sourcesContent": ["<template>\n\t<view class=\"container\">\n\t\t<!-- 顶部导航栏 -->\n\t\t<view class=\"header\">\n\t\t\t<button class=\"back-btn\" @click=\"goBack\">返回</button>\n\t\t\t<text class=\"header-title\">WebDAV配置</text>\n\t\t\t<view class=\"header-placeholder\"></view>\n\t\t</view>\n\t\t\n\t\t<!-- 配置表单 -->\n\t\t<scroll-view class=\"config-content\" scroll-y=\"true\">\n\t\t\t<view class=\"form-container\">\n\t\t\t\t<view class=\"form-section\">\n\t\t\t\t\t<text class=\"section-title\">服务器设置</text>\n\t\t\t\t\t\n\t\t\t\t\t<view class=\"form-item\">\n\t\t\t\t\t\t<text class=\"form-label\">服务器地址</text>\n\t\t\t\t\t\t<input \n\t\t\t\t\t\t\tclass=\"form-input\" \n\t\t\t\t\t\t\tv-model=\"config['serverUrl']\" \n\t\t\t\t\t\t\tplaceholder=\"https://your-webdav-server.com\"\n\t\t\t\t\t\t\ttype=\"url\"\n\t\t\t\t\t\t/>\n\t\t\t\t\t</view>\n\t\t\t\t\t\n\t\t\t\t\t<view class=\"form-item\">\n\t\t\t\t\t\t<text class=\"form-label\">端口号</text>\n\t\t\t\t\t\t<input \n\t\t\t\t\t\t\tclass=\"form-input\" \n\t\t\t\t\t\t\tv-model=\"config['port']\" \n\t\t\t\t\t\t\tplaceholder=\"443\"\n\t\t\t\t\t\t\ttype=\"number\"\n\t\t\t\t\t\t/>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t\t\n\t\t\t\t<view class=\"form-section\">\n\t\t\t\t\t<text class=\"section-title\">认证信息</text>\n\t\t\t\t\t\n\t\t\t\t\t<view class=\"form-item\">\n\t\t\t\t\t\t<text class=\"form-label\">用户名</text>\n\t\t\t\t\t\t<input \n\t\t\t\t\t\t\tclass=\"form-input\" \n\t\t\t\t\t\t\tv-model=\"config['username']\" \n\t\t\t\t\t\t\tplaceholder=\"请输入用户名\"\n\t\t\t\t\t\t\ttype=\"text\"\n\t\t\t\t\t\t/>\n\t\t\t\t\t</view>\n\t\t\t\t\t\n\t\t\t\t\t<view class=\"form-item\">\n\t\t\t\t\t\t<text class=\"form-label\">密码</text>\n\t\t\t\t\t\t<input \n\t\t\t\t\t\t\tclass=\"form-input\" \n\t\t\t\t\t\t\tv-model=\"config['password']\" \n\t\t\t\t\t\t\tplaceholder=\"请输入密码\"\n\t\t\t\t\t\t\ttype=\"password\"\n\t\t\t\t\t\t/>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t\t\n\t\t\t\t<view class=\"form-section\">\n\t\t\t\t\t<text class=\"section-title\">高级设置</text>\n\t\t\t\t\t\n\t\t\t\t\t<view class=\"form-item\">\n\t\t\t\t\t\t<text class=\"form-label\">根目录</text>\n\t\t\t\t\t\t<input \n\t\t\t\t\t\t\tclass=\"form-input\" \n\t\t\t\t\t\t\tv-model=\"config['rootPath']\" \n\t\t\t\t\t\t\tplaceholder=\"/movies\"\n\t\t\t\t\t\t\ttype=\"text\"\n\t\t\t\t\t\t/>\n\t\t\t\t\t</view>\n\t\t\t\t\t\n\t\t\t\t\t<view class=\"form-item\">\n\t\t\t\t\t\t<view class=\"form-row\">\n\t\t\t\t\t\t\t<text class=\"form-label\">启用HTTPS</text>\n\t\t\t\t\t\t\t<switch \n\t\t\t\t\t\t\t\tclass=\"form-switch\" \n\t\t\t\t\t\t\t:checked=\"config['useHttps']\"\n\t\t\t\t\t\t\t/>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t\n\t\t\t\t\t<view class=\"form-item\">\n\t\t\t\t\t\t<view class=\"form-row\">\n\t\t\t\t\t\t\t<text class=\"form-label\">自动连接</text>\n\t\t\t\t\t\t\t<switch \n\t\t\t\t\t\t\t\tclass=\"form-switch\" \n\t\t\t\t\t\t\t:checked=\"config['autoConnect']\" \n\t\t\t\t\t\t\t/>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t\t\n\t\t\t\t<!-- 操作按钮 -->\n\t\t\t\t<view class=\"button-group\">\n\t\t\t\t\t<button class=\"test-btn\" @click=\"testConnection\">测试连接</button>\n\t\t\t\t\t<button class=\"save-btn\" @click=\"saveConfig\">保存配置</button>\n\t\t\t\t</view>\n\t\t\t\t\n\t\t\t\t<!-- 连接状态 -->\n\t\t\t\t<view class=\"status-section\" v-if=\"connectionStatus\">\n\t\t\t\t\t<text class=\"status-text\" :class=\"statusClass\">{{connectionStatus}}</text>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</scroll-view>\n\t</view>\n</template>\n\n<script>\r\n\ttype Config = {\n\t\tserverUrl: string\n\t\tport: string\n\t\tusername: string\n\t\tpassword: string\n\t\trootPath: string\n\t\tuseHttps: boolean\n\t\tautoConnect: boolean\n\t}\n\texport default {\n\t\tdata() {\n\t\t\treturn {\n\t\t\t\tconfig: {\n\t\t\t\t\tserverUrl: '',\n\t\t\t\t\tport: '443',\n\t\t\t\t\tusername: '',\n\t\t\t\t\tpassword: '',\n\t\t\t\t\trootPath: '/movies',\n\t\t\t\t\tuseHttps: true,\n\t\t\t\t\tautoConnect: false\n\t\t\t\t},\n\t\t\t\tconnectionStatus: '',\n\t\t\t\tstatusClass: ''\n\t\t\t}\n\t\t},\n\t\tonLoad() {\n\t\t\tthis.loadConfig()\n\t\t},\n\t\tmethods: {\n\t\t\tgoBack() {\n\t\t\t\tuni.navigateBack()\n\t\t\t},\n\t\t\t\n\t\t\tloadConfig() {\n\t\t\t\t// 从本地存储加载配置\n\t\t\t\ttry {\n\t\t\t\t\tconst savedConfig = uni.getStorageSync('webdav_config')\n\t\t\t\t\tif (savedConfig != null) {\n\t\t\t\t\t\tconst configData = savedConfig as UTSJSONObject\n\t\t\t\t\t\tthis.config['serverUrl'] = configData['serverUrl'] as string ?? ''\n\t\t\t\t\t\tthis.config['port'] = configData['port'] as string ?? '443'\n\t\t\t\t\t\tthis.config['username'] = configData['username'] as string ?? ''\n\t\t\t\t\t\tthis.config['password'] = configData['password'] as string ?? ''\n\t\t\t\t\t\tthis.config['rootPath'] = configData['rootPath'] as string ?? '/movies'\n\t\t\t\t\t\tthis.config['useHttps'] = configData['useHttps'] as boolean ?? true\n\t\t\t\t\t\tthis.config['autoConnect'] = configData['autoConnect'] as boolean ?? false\n\t\t\t\t\t}\n\t\t\t\t} catch (e) {\n\t\t\t\t\tconsole.error(e as any)\n\t\t\t\t}\n\t\t\t},\n\t\t\t\n\t\t\tsaveConfig() {\n\t\t\t\t// 验证必填字段\n\t\t\tif (this.config['serverUrl'] == null || (this.config['serverUrl'] as string).trim() == '') {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '请输入服务器地址',\n\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t})\n\t\t\t\t\treturn\n\t\t\t\t}\n\t\t\t\t\n\t\t\tif (this.config['username'] == null || (this.config['username'] as string).trim() == '') {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '请输入用户名',\n\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t})\n\t\t\t\t\treturn\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\t// 保存到本地存储\n\t\t\t\ttry {\n\t\t\t\t\tuni.setStorageSync('webdav_config', this.config)\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '配置保存成功',\n\t\t\t\t\t\ticon: 'success'\n\t\t\t\t\t})\n\t\t\t\t} catch (e) {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '保存失败',\n\t\t\t\t\t\ticon: 'error'\n\t\t\t\t\t})\n\t\t\t\t\tconsole.error(e as any)\n\t\t\t\t}\n\t\t\t},\n\t\t\t\n\t\t\tasync testConnection() {\n\t\t\tif ((this.config['serverUrl'] == null || (this.config['serverUrl'] as string).trim() == '') || \n\t\t\t\t(this.config['username'] == null || (this.config['username'] as string).trim() == '')) {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '请先填写服务器地址和用户名',\n\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t})\n\t\t\t\t\treturn\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\tthis.connectionStatus = '正在测试连接...'\n\t\t\t\tthis.statusClass = 'status-testing'\n\t\t\t}\n\t\t}\n\t}\n</script>\n\n<style>\n\t.container {\n\t\tflex: 1;\n\t\tbackground-color: #1a1a1a;\n\t\tmin-height: 100vh;\n\t}\n\n\t.header {\n\t\tdisplay: flex;\n\t\tjustify-content: space-between;\n\t\talign-items: center;\n\t\tpadding: 20px;\n\t\tbackground-color: #2d2d2d;\n\t\tborder-bottom: 1px solid #404040;\n\t}\n\n\t.back-btn {\n\t\tbackground-color: #404040;\n\t\tcolor: #ffffff;\n\t\tborder: none;\n\t\tborder-radius: 8px;\n\t\tpadding: 8px 16px;\n\t\tfont-size: 14px;\n\t\tcursor: pointer;\n\t}\n\n\t.back-btn:hover {\n\t\tbackground-color: #505050;\n\t}\n\n\t.header-title {\n\t\tfont-size: 20px;\n\t\tfont-weight: bold;\n\t\tcolor: #ffffff;\n\t}\n\n\t.header-placeholder {\n\t\twidth: 60px;\n\t}\n\n\t.config-content {\n\t\tflex: 1;\n\t\tpadding: 20px;\n\t}\n\n\t.form-container {\n\t\tmax-width: 600px;\n\t\tmargin: 0 auto;\n\t}\n\n\t.form-section {\n\t\tmargin-bottom: 30px;\n\t\tbackground-color: #2d2d2d;\n\t\tborder-radius: 12px;\n\t\tpadding: 20px;\n\t}\n\n\t.section-title {\n\t\tfont-size: 18px;\n\t\tfont-weight: bold;\n\t\tcolor: #ffffff;\n\t\tmargin-bottom: 20px;\n\t\tdisplay: block;\n\t}\n\n\t.form-item {\n\t\tmargin-bottom: 20px;\n\t}\n\n\t.form-item:last-child {\n\t\tmargin-bottom: 0;\n\t}\n\n\t.form-label {\n\t\tfont-size: 16px;\n\t\tcolor: #cccccc;\n\t\tmargin-bottom: 8px;\n\t\tdisplay: block;\n\t}\n\n\t.form-input {\n\t\twidth: 100%;\n\t\tpadding: 12px 16px;\n\t\tbackground-color: #404040;\n\t\tborder: 1px solid #555555;\n\t\tborder-radius: 8px;\n\t\tcolor: #ffffff;\n\t\tfont-size: 16px;\n\t}\n\n\t.form-input:focus {\n\t\tborder-color: #007AFF;\n\t\toutline: none;\n\t}\n\n\t.form-row {\n\t\tdisplay: flex;\n\t\tjustify-content: space-between;\n\t\talign-items: center;\n\t}\n\n\t.form-switch {\n\t\ttransform: scale(1.2);\n\t}\n\n\t.button-group {\n\t\tdisplay: flex;\n\t\tgap: 15px;\n\t\tmargin-top: 30px;\n\t}\n\n\t.test-btn, .save-btn {\n\t\tflex: 1;\n\t\tpadding: 15px;\n\t\tborder: none;\n\t\tborder-radius: 8px;\n\t\tfont-size: 16px;\n\t\tfont-weight: bold;\n\t\tcursor: pointer;\n\t}\n\n\t.test-btn {\n\t\tbackground-color: #FF9500;\n\t\tcolor: #ffffff;\n\t}\n\n\t.test-btn:hover {\n\t\tbackground-color: #E6850E;\n\t}\n\n\t.save-btn {\n\t\tbackground-color: #007AFF;\n\t\tcolor: #ffffff;\n\t}\n\n\t.save-btn:hover {\n\t\tbackground-color: #0056CC;\n\t}\n\n\t.status-section {\n\t\tmargin-top: 20px;\n\t\ttext-align: center;\n\t}\n\n\t.status-text {\n\t\tfont-size: 16px;\n\t\tfont-weight: bold;\n\t\tpadding: 12px 20px;\n\t\tborder-radius: 8px;\n\t\tdisplay: inline-block;\n\t}\n\n\t.status-testing {\n\t\tbackground-color: #FF9500;\n\t\tcolor: #ffffff;\n\t}\n\n\t.status-success {\n\t\tbackground-color: #34C759;\n\t\tcolor: #ffffff;\n\t}\n\n\t.status-error {\n\t\tbackground-color: #FF3B30;\n\t\tcolor: #ffffff;\n\t}\n</style>\n", null], "names": [], "mappings": ";;;;;;;;;;;;;+BAkJ6B;+BANrB;+BA0CC;+BA3DJ;AAJC;;eAgBJ,sBAAM;YACL,IAAI,CAAC,UAAU;QAChB;;;;;;;;eAxID,IAyGO,QAAA,IAzGD,WAAM,cAAW;YAEtB,IAIO,QAAA,IAJD,WAAM,WAAQ;gBACnB,IAAoD,UAAA,IAA5C,WAAM,YAAY,aAAO,KAAA,MAAM,GAAE,MAAE,CAAA,EAAA;oBAAA;iBAAA;gBAC3C,IAA0C,QAAA,IAApC,WAAM,iBAAe;gBAC3B,IAAwC,QAAA,IAAlC,WAAM;;YAIb,IA+Fc,eAAA,IA/FD,WAAM,kBAAiB,cAAS;gBAC5C,IA6FO,QAAA,IA7FD,WAAM,mBAAgB;oBAC3B,IAsBO,QAAA,IAtBD,WAAM,iBAAc;wBACzB,IAAwC,QAAA,IAAlC,WAAM,kBAAgB;wBAE5B,IAQO,QAAA,IARD,WAAM,cAAW;4BACtB,IAAqC,QAAA,IAA/B,WAAM,eAAa;4BACzB,IAKE,SAAA,IAJD,WAAM,8BACG,KAAA,MAAM,CAAA,YAAA;gCAAN,KAAA,MAAM,CAAA,YAAA,GAAA,SAAA,MAAA,CAAA,KAAA;4BAAA;8BACf,iBAAY,kCACZ,UAAK;;;;;wBAIP,IAQO,QAAA,IARD,WAAM,cAAW;4BACtB,IAAmC,QAAA,IAA7B,WAAM,eAAa;4BACzB,IAKE,SAAA,IAJD,WAAM,8BACG,KAAA,MAAM,CAAA,OAAA;gCAAN,KAAA,MAAM,CAAA,OAAA,GAAA,SAAA,MAAA,CAAA,KAAA;4BAAA;8BACf,iBAAY,OACZ,UAAK;;;;;;oBAKR,IAsBO,QAAA,IAtBD,WAAM,iBAAc;wBACzB,IAAuC,QAAA,IAAjC,WAAM,kBAAgB;wBAE5B,IAQO,QAAA,IARD,WAAM,cAAW;4BACtB,IAAmC,QAAA,IAA7B,WAAM,eAAa;4BACzB,IAKE,SAAA,IAJD,WAAM,8BACG,KAAA,MAAM,CAAA,WAAA;gCAAN,KAAA,MAAM,CAAA,WAAA,GAAA,SAAA,MAAA,CAAA,KAAA;4BAAA;8BACf,iBAAY,UACZ,UAAK;;;;;wBAIP,IAQO,QAAA,IARD,WAAM,cAAW;4BACtB,IAAkC,QAAA,IAA5B,WAAM,eAAa;4BACzB,IAKE,SAAA,IAJD,WAAM,8BACG,KAAA,MAAM,CAAA,WAAA;gCAAN,KAAA,MAAM,CAAA,WAAA,GAAA,SAAA,MAAA,CAAA,KAAA;4BAAA;8BACf,iBAAY,SACZ,UAAK;;;;;;oBAKR,IAgCO,QAAA,IAhCD,WAAM,iBAAc;wBACzB,IAAuC,QAAA,IAAjC,WAAM,kBAAgB;wBAE5B,IAQO,QAAA,IARD,WAAM,cAAW;4BACtB,IAAmC,QAAA,IAA7B,WAAM,eAAa;4BACzB,IAKE,SAAA,IAJD,WAAM,8BACG,KAAA,MAAM,CAAA,WAAA;gCAAN,KAAA,MAAM,CAAA,WAAA,GAAA,SAAA,MAAA,CAAA,KAAA;4BAAA;8BACf,iBAAY,WACZ,UAAK;;;;;wBAIP,IAQO,QAAA,IARD,WAAM,cAAW;4BACtB,IAMO,QAAA,IAND,WAAM,aAAU;gCACrB,IAAuC,QAAA,IAAjC,WAAM,eAAa;gCACzB,IAGE,mBAAA,IAFD,WAAM,eACN,aAAS,KAAA,MAAM,CAAA,WAAA;;;;;wBAKlB,IAQO,QAAA,IARD,WAAM,cAAW;4BACtB,IAMO,QAAA,IAND,WAAM,aAAU;gCACrB,IAAoC,QAAA,IAA9B,WAAM,eAAa;gCACzB,IAGE,mBAAA,IAFD,WAAM,eACN,aAAS,KAAA,MAAM,CAAA,cAAA;;;;;;oBAOnB,IAGO,QAAA,IAHD,WAAM,iBAAc;wBACzB,IAA8D,UAAA,IAAtD,WAAM,YAAY,aAAO,KAAA,cAAc,GAAE,QAAI,CAAA,EAAA;4BAAA;yBAAA;wBACrD,IAA0D,UAAA,IAAlD,WAAM,YAAY,aAAO,KAAA,UAAU,GAAE,QAAI,CAAA,EAAA;4BAAA;yBAAA;;+BAIf,KAAA,gBAAgB;wBAAnD,IAEO,QAAA,gBAFD,WAAM;4BACX,IAA0E,QAAA,IAApE,WAAK,IAAA;gCAAC;gCAAsB,KAAA,WAAW;6BAAA,QAAI,KAAA,gBAAgB,GAAA,CAAA;;;;;;;;;aAoBlE;aASA;aACA;;;mBAVA,YAAQ;YACP,IAAA,YAAW;YACX,IAAA,OAAM;YACN,IAAA,WAAU;YACV,IAAA,WAAU;YACV,IAAA,WAAU;YACV,IAAA,WAAU,IAAI;YACd,IAAA,cAAa,KAAI;SACjB,EACD,sBAAkB,IAClB,iBAAa;;aAOd;aAAA,gBAAM;QACL;IACD;aAEA;aAAA,oBAAU;QAET,IAAI;YACH,IAAM,cAAc,mBAAmB;YACvC,IAAI,eAAe,IAAI,EAAE;gBACxB,IAAM,aAAa,YAAU,EAAA,CAAK;gBAClC,IAAI,CAAC,MAAM,CAAC,YAAW,GAAI,UAAU,CAAC,YAAW,CAAA,EAAA,CAAK,MAAK,IAAK;gBAChE,IAAI,CAAC,MAAM,CAAC,OAAM,GAAI,UAAU,CAAC,OAAM,CAAA,EAAA,CAAK,MAAK,IAAK;gBACtD,IAAI,CAAC,MAAM,CAAC,WAAU,GAAI,UAAU,CAAC,WAAU,CAAA,EAAA,CAAK,MAAK,IAAK;gBAC9D,IAAI,CAAC,MAAM,CAAC,WAAU,GAAI,UAAU,CAAC,WAAU,CAAA,EAAA,CAAK,MAAK,IAAK;gBAC9D,IAAI,CAAC,MAAM,CAAC,WAAU,GAAI,UAAU,CAAC,WAAU,CAAA,EAAA,CAAK,MAAK,IAAK;gBAC9D,IAAI,CAAC,MAAM,CAAC,WAAU,GAAI,UAAU,CAAC,WAAU,CAAA,EAAA,CAAK,OAAM,IAAK,IAAG;gBAClE,IAAI,CAAC,MAAM,CAAC,cAAa,GAAI,UAAU,CAAC,cAAa,CAAA,EAAA,CAAK,OAAM,IAAK,KAAI;;;SAEzE,OAAO,cAAG;YACX,QAAQ,KAAK,CAAC,EAAA,EAAA,CAAK,GAAG,EAAA;;IAExB;aAEA;aAAA,oBAAU;QAEV,IAAI,IAAI,CAAC,MAAM,CAAC,YAAW,IAAK,IAAG,IAAK,CAAC,IAAI,CAAC,MAAM,CAAC,YAAW,CAAA,EAAA,CAAK,MAAM,EAAE,IAAI,MAAM,IAAI;YACzF,+BACC,QAAO,YACP,OAAM;YAEP;;QAGF,IAAI,IAAI,CAAC,MAAM,CAAC,WAAU,IAAK,IAAG,IAAK,CAAC,IAAI,CAAC,MAAM,CAAC,WAAU,CAAA,EAAA,CAAK,MAAM,EAAE,IAAI,MAAM,IAAI;YACvF,+BACC,QAAO,UACP,OAAM;YAEP;;QAID,IAAI;YACH,mBAAmB,iBAAiB,IAAI,CAAC,MAAM;YAC/C,+BACC,QAAO,UACP,OAAM;;SAEN,OAAO,cAAG;YACX,+BACC,QAAO,QACP,OAAM;YAEP,QAAQ,KAAK,CAAC,EAAA,EAAA,CAAK,GAAG,EAAA;;IAExB;aAEM;aAAA,yBAAc,WAAA,IAAA,EAAA;QAAA,OAAA,eAAA;gBACpB,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,YAAW,IAAK,IAAG,IAAK,CAAC,IAAI,CAAC,MAAM,CAAC,YAAW,CAAA,EAAA,CAAK,MAAM,EAAE,IAAI,MAAM,EAAE,KACzF,CAAC,IAAI,CAAC,MAAM,CAAC,WAAU,IAAK,IAAG,IAAK,CAAC,IAAI,CAAC,MAAM,CAAC,WAAU,CAAA,EAAA,CAAK,MAAM,EAAE,IAAI,MAAM,EAAE,GAAG;oBACtF,+BACC,QAAO,iBACP,OAAM;oBAEP;;gBAGD,IAAI,CAAC,gBAAe,GAAI;gBACxB,IAAI,CAAC,WAAU,GAAI;SACpB;IAAA;;;;;;;;;;;;;;;;;;;;AAEF"}