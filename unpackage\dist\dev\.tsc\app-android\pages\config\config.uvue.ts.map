{"version": 3, "file": "pages/config/config.uvue", "names": [], "sources": ["pages/config/config.uvue"], "sourcesContent": ["<template>\n\t<view class=\"container\">\n\t\t<!-- 顶部导航栏 -->\n\t\t<view class=\"header\">\n\t\t\t<button class=\"back-btn\" @click=\"goBack\">返回</button>\n\t\t\t<text class=\"header-title\">WebDAV配置</text>\n\t\t\t<view class=\"header-placeholder\"></view>\n\t\t</view>\n\t\t\n\t\t<!-- 配置表单 -->\n\t\t<scroll-view class=\"config-content\" scroll-y=\"true\">\n\t\t\t<view class=\"form-container\">\n\t\t\t\t<view class=\"form-section\">\n\t\t\t\t\t<text class=\"section-title\">服务器设置</text>\n\t\t\t\t\t\n\t\t\t\t\t<view class=\"form-item\">\n\t\t\t\t\t\t<text class=\"form-label\">服务器地址</text>\n\t\t\t\t\t\t<input \n\t\t\t\t\t\t\tclass=\"form-input\" \n\t\t\t\t\t\t\tv-model=\"config['serverUrl']\" \n\t\t\t\t\t\t\tplaceholder=\"https://your-webdav-server.com\"\n\t\t\t\t\t\t\ttype=\"url\"\n\t\t\t\t\t\t/>\n\t\t\t\t\t</view>\n\t\t\t\t\t\n\t\t\t\t\t<view class=\"form-item\">\n\t\t\t\t\t\t<text class=\"form-label\">端口号</text>\n\t\t\t\t\t\t<input \n\t\t\t\t\t\t\tclass=\"form-input\" \n\t\t\t\t\t\t\tv-model=\"config['port']\" \n\t\t\t\t\t\t\tplaceholder=\"443\"\n\t\t\t\t\t\t\ttype=\"number\"\n\t\t\t\t\t\t/>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t\t\n\t\t\t\t<view class=\"form-section\">\n\t\t\t\t\t<text class=\"section-title\">认证信息</text>\n\t\t\t\t\t\n\t\t\t\t\t<view class=\"form-item\">\n\t\t\t\t\t\t<text class=\"form-label\">用户名</text>\n\t\t\t\t\t\t<input \n\t\t\t\t\t\t\tclass=\"form-input\" \n\t\t\t\t\t\t\tv-model=\"config['username']\" \n\t\t\t\t\t\t\tplaceholder=\"请输入用户名\"\n\t\t\t\t\t\t\ttype=\"text\"\n\t\t\t\t\t\t/>\n\t\t\t\t\t</view>\n\t\t\t\t\t\n\t\t\t\t\t<view class=\"form-item\">\n\t\t\t\t\t\t<text class=\"form-label\">密码</text>\n\t\t\t\t\t\t<input \n\t\t\t\t\t\t\tclass=\"form-input\" \n\t\t\t\t\t\t\tv-model=\"config['password']\" \n\t\t\t\t\t\t\tplaceholder=\"请输入密码\"\n\t\t\t\t\t\t\ttype=\"password\"\n\t\t\t\t\t\t/>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t\t\n\t\t\t\t<view class=\"form-section\">\n\t\t\t\t\t<text class=\"section-title\">高级设置</text>\n\t\t\t\t\t\n\t\t\t\t\t<view class=\"form-item\">\n\t\t\t\t\t\t<text class=\"form-label\">根目录</text>\n\t\t\t\t\t\t<input \n\t\t\t\t\t\t\tclass=\"form-input\" \n\t\t\t\t\t\t\tv-model=\"config['rootPath']\" \n\t\t\t\t\t\t\tplaceholder=\"/movies\"\n\t\t\t\t\t\t\ttype=\"text\"\n\t\t\t\t\t\t/>\n\t\t\t\t\t</view>\n\t\t\t\t\t\n\t\t\t\t\t<view class=\"form-item\">\n\t\t\t\t\t\t<view class=\"form-row\">\n\t\t\t\t\t\t\t<text class=\"form-label\">启用HTTPS</text>\n\t\t\t\t\t\t\t<switch \n\t\t\t\t\t\t\t\tclass=\"form-switch\" \n\t\t\t\t\t\t\t:checked=\"config['useHttps']\"\n\t\t\t\t\t\t\t/>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t\n\t\t\t\t\t<view class=\"form-item\">\n\t\t\t\t\t\t<view class=\"form-row\">\n\t\t\t\t\t\t\t<text class=\"form-label\">自动连接</text>\n\t\t\t\t\t\t\t<switch \n\t\t\t\t\t\t\t\tclass=\"form-switch\" \n\t\t\t\t\t\t\t:checked=\"config['autoConnect']\" \n\t\t\t\t\t\t\t/>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t\t\n\t\t\t\t<!-- 操作按钮 -->\n\t\t\t\t<view class=\"button-group\">\n\t\t\t\t\t<button class=\"test-btn\" @click=\"testConnection\">测试连接</button>\n\t\t\t\t\t<button class=\"save-btn\" @click=\"saveConfig\">保存配置</button>\n\t\t\t\t</view>\n\t\t\t\t\n\t\t\t\t<!-- 连接状态 -->\n\t\t\t\t<view class=\"status-section\" v-if=\"connectionStatus\">\n\t\t\t\t\t<text class=\"status-text\" :class=\"statusClass\">{{connectionStatus}}</text>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</scroll-view>\n\t</view>\n</template>\n\n<script>\r\n\ttype Config = {\n\t\tserverUrl: string\n\t\tport: string\n\t\tusername: string\n\t\tpassword: string\n\t\trootPath: string\n\t\tuseHttps: boolean\n\t\tautoConnect: boolean\n\t}\n\texport default {\n\t\tdata() {\n\t\t\treturn {\n\t\t\t\tconfig: {\n\t\t\t\t\tserverUrl: '',\n\t\t\t\t\tport: '443',\n\t\t\t\t\tusername: '',\n\t\t\t\t\tpassword: '',\n\t\t\t\t\trootPath: '/movies',\n\t\t\t\t\tuseHttps: true,\n\t\t\t\t\tautoConnect: false\n\t\t\t\t},\n\t\t\t\tconnectionStatus: '',\n\t\t\t\tstatusClass: ''\n\t\t\t}\n\t\t},\n\t\tonLoad() {\n\t\t\tthis.loadConfig()\n\t\t},\n\t\tmethods: {\n\t\t\tgoBack() {\n\t\t\t\tuni.navigateBack()\n\t\t\t},\n\t\t\t\n\t\t\tloadConfig() {\n\t\t\t\t// 从本地存储加载配置\n\t\t\t\ttry {\n\t\t\t\t\tconst savedConfig = uni.getStorageSync('webdav_config')\n\t\t\t\t\tif (savedConfig != null) {\n\t\t\t\t\t\tconst configData = savedConfig as UTSJSONObject\n\t\t\t\t\t\tthis.config['serverUrl'] = configData['serverUrl'] as string ?? ''\n\t\t\t\t\t\tthis.config['port'] = configData['port'] as string ?? '443'\n\t\t\t\t\t\tthis.config['username'] = configData['username'] as string ?? ''\n\t\t\t\t\t\tthis.config['password'] = configData['password'] as string ?? ''\n\t\t\t\t\t\tthis.config['rootPath'] = configData['rootPath'] as string ?? '/movies'\n\t\t\t\t\t\tthis.config['useHttps'] = configData['useHttps'] as boolean ?? true\n\t\t\t\t\t\tthis.config['autoConnect'] = configData['autoConnect'] as boolean ?? false\n\t\t\t\t\t}\n\t\t\t\t} catch (e) {\n\t\t\t\t\tconsole.error(e as any)\n\t\t\t\t}\n\t\t\t},\n\t\t\t\n\t\t\tsaveConfig() {\n\t\t\t\t// 验证必填字段\n\t\t\tif (this.config['serverUrl'] == null || (this.config['serverUrl'] as string).trim() == '') {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '请输入服务器地址',\n\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t})\n\t\t\t\t\treturn\n\t\t\t\t}\n\t\t\t\t\n\t\t\tif (this.config['username'] == null || (this.config['username'] as string).trim() == '') {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '请输入用户名',\n\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t})\n\t\t\t\t\treturn\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\t// 保存到本地存储\n\t\t\t\ttry {\n\t\t\t\t\tuni.setStorageSync('webdav_config', this.config)\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '配置保存成功',\n\t\t\t\t\t\ticon: 'success'\n\t\t\t\t\t})\n\t\t\t\t} catch (e) {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '保存失败',\n\t\t\t\t\t\ticon: 'error'\n\t\t\t\t\t})\n\t\t\t\t\tconsole.error(e as any)\n\t\t\t\t}\n\t\t\t},\n\t\t\t\n\t\t\tasync testConnection() {\n\t\t\tif ((this.config['serverUrl'] == null || (this.config['serverUrl'] as string).trim() == '') || \n\t\t\t\t(this.config['username'] == null || (this.config['username'] as string).trim() == '')) {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '请先填写服务器地址和用户名',\n\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t})\n\t\t\t\t\treturn\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\tthis.connectionStatus = '正在测试连接...'\n\t\t\t\tthis.statusClass = 'status-testing'\n\t\t\t}\n\t\t}\n\t}\n</script>\n\n<style>\n\t.container {\n\t\tflex: 1;\n\t\tbackground-color: #1a1a1a;\n\t\tmin-height: 100vh;\n\t}\n\n\t.header {\n\t\tdisplay: flex;\n\t\tjustify-content: space-between;\n\t\talign-items: center;\n\t\tpadding: 20px;\n\t\tbackground-color: #2d2d2d;\n\t\tborder-bottom: 1px solid #404040;\n\t}\n\n\t.back-btn {\n\t\tbackground-color: #404040;\n\t\tcolor: #ffffff;\n\t\tborder: none;\n\t\tborder-radius: 8px;\n\t\tpadding: 8px 16px;\n\t\tfont-size: 14px;\n\t\tcursor: pointer;\n\t}\n\n\t.back-btn:hover {\n\t\tbackground-color: #505050;\n\t}\n\n\t.header-title {\n\t\tfont-size: 20px;\n\t\tfont-weight: bold;\n\t\tcolor: #ffffff;\n\t}\n\n\t.header-placeholder {\n\t\twidth: 60px;\n\t}\n\n\t.config-content {\n\t\tflex: 1;\n\t\tpadding: 20px;\n\t}\n\n\t.form-container {\n\t\tmax-width: 600px;\n\t\tmargin: 0 auto;\n\t}\n\n\t.form-section {\n\t\tmargin-bottom: 30px;\n\t\tbackground-color: #2d2d2d;\n\t\tborder-radius: 12px;\n\t\tpadding: 20px;\n\t}\n\n\t.section-title {\n\t\tfont-size: 18px;\n\t\tfont-weight: bold;\n\t\tcolor: #ffffff;\n\t\tmargin-bottom: 20px;\n\t\tdisplay: block;\n\t}\n\n\t.form-item {\n\t\tmargin-bottom: 20px;\n\t}\n\n\t.form-item:last-child {\n\t\tmargin-bottom: 0;\n\t}\n\n\t.form-label {\n\t\tfont-size: 16px;\n\t\tcolor: #cccccc;\n\t\tmargin-bottom: 8px;\n\t\tdisplay: block;\n\t}\n\n\t.form-input {\n\t\twidth: 100%;\n\t\tpadding: 12px 16px;\n\t\tbackground-color: #404040;\n\t\tborder: 1px solid #555555;\n\t\tborder-radius: 8px;\n\t\tcolor: #ffffff;\n\t\tfont-size: 16px;\n\t}\n\n\t.form-input:focus {\n\t\tborder-color: #007AFF;\n\t\toutline: none;\n\t}\n\n\t.form-row {\n\t\tdisplay: flex;\n\t\tjustify-content: space-between;\n\t\talign-items: center;\n\t}\n\n\t.form-switch {\n\t\ttransform: scale(1.2);\n\t}\n\n\t.button-group {\n\t\tdisplay: flex;\n\t\tgap: 15px;\n\t\tmargin-top: 30px;\n\t}\n\n\t.test-btn, .save-btn {\n\t\tflex: 1;\n\t\tpadding: 15px;\n\t\tborder: none;\n\t\tborder-radius: 8px;\n\t\tfont-size: 16px;\n\t\tfont-weight: bold;\n\t\tcursor: pointer;\n\t}\n\n\t.test-btn {\n\t\tbackground-color: #FF9500;\n\t\tcolor: #ffffff;\n\t}\n\n\t.test-btn:hover {\n\t\tbackground-color: #E6850E;\n\t}\n\n\t.save-btn {\n\t\tbackground-color: #007AFF;\n\t\tcolor: #ffffff;\n\t}\n\n\t.save-btn:hover {\n\t\tbackground-color: #0056CC;\n\t}\n\n\t.status-section {\n\t\tmargin-top: 20px;\n\t\ttext-align: center;\n\t}\n\n\t.status-text {\n\t\tfont-size: 16px;\n\t\tfont-weight: bold;\n\t\tpadding: 12px 20px;\n\t\tborder-radius: 8px;\n\t\tdisplay: inline-block;\n\t}\n\n\t.status-testing {\n\t\tbackground-color: #FF9500;\n\t\tcolor: #ffffff;\n\t}\n\n\t.status-success {\n\t\tbackground-color: #34C759;\n\t\tcolor: #ffffff;\n\t}\n\n\t.status-error {\n\t\tbackground-color: #FF3B30;\n\t\tcolor: #ffffff;\n\t}\n</style>\n"], "mappings": ";CA8GC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE;EACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;EAChB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;EACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;EACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;EACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;EACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;CACpB;CACA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACd,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;GACN,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;KACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;KACb,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;KACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;KACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;KACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;KACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;KACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IAClB,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;IACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;GACf;EACD,CAAC;EACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;GACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACjB,CAAC;EACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;GACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;GAClB,CAAC;;GAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACZ,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACX,CAAC,CAAC,EAAE;KACH,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;KACtD,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE;MACxB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC9C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC;MACjE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MAC1D,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC;MAC/D,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC;MAC/D,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACtE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;MAClE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;KAC1E;IACD,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;KACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;IACvB;GACD,CAAC;;GAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACZ,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;GACT,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE;KACzF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACb,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACjB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;KACZ,CAAC;KACD,CAAC,CAAC,CAAC,CAAC,CAAC;IACN;;GAED,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE;KACvF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACb,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACf,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;KACZ,CAAC;KACD,CAAC,CAAC,CAAC,CAAC,CAAC;IACN;;IAEA,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACT,CAAC,CAAC,EAAE;KACH,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;KAC/C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACb,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACf,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;KACf,CAAC;IACF,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;KACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACb,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACb,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;KACb,CAAC;KACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;IACvB;GACD,CAAC;;GAED,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;GACvB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC;IAC5F,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE;KACtF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACb,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACtB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;KACZ,CAAC;KACD,CAAC,CAAC,CAAC,CAAC,CAAC;IACN;;IAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAClC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;GACnC;EACD;CACD;;;;;;;;SAjNA,IAyGO,cAzGD,KAAK,EAAC,WAAW;IAEtB,IAIO,cAJD,KAAK,EAAC,QAAQ;MACnB,IAAoD;QAA5C,KAAK,EAAC,UAAU;QAAE,OAAK,EAAE,WAAM;UAAE,IAAE;MAC3C,IAA0C,cAApC,KAAK,EAAC,cAAc,KAAC,UAAQ;MACnC,IAAwC,cAAlC,KAAK,EAAC,oBAAoB;;IAIjC,IA+Fc;MA/FD,KAAK,EAAC,gBAAgB;MAAC,UAAQ,EAAC,MAAM;;MAClD,IA6FO,cA7FD,KAAK,EAAC,gBAAgB;QAC3B,IAsBO,cAtBD,KAAK,EAAC,cAAc;UACzB,IAAwC,cAAlC,KAAK,EAAC,eAAe,KAAC,OAAK;UAEjC,IAQO,cARD,KAAK,EAAC,WAAW;YACtB,IAAqC,cAA/B,KAAK,EAAC,YAAY,KAAC,OAAK;YAC9B,IAKE;cAJD,KAAK,EAAC,YAAY;0BACT,WAAM;oDAAN,WAAM;cACf,WAAW,EAAC,gCAAgC;cAC5C,IAAI,EAAC,KAAK;;;UAIZ,IAQO,cARD,KAAK,EAAC,WAAW;YACtB,IAAmC,cAA7B,KAAK,EAAC,YAAY,KAAC,KAAG;YAC5B,IAKE;cAJD,KAAK,EAAC,YAAY;0BACT,WAAM;oDAAN,WAAM;cACf,WAAW,EAAC,KAAK;cACjB,IAAI,EAAC,QAAQ;;;;QAKhB,IAsBO,cAtBD,KAAK,EAAC,cAAc;UACzB,IAAuC,cAAjC,KAAK,EAAC,eAAe,KAAC,MAAI;UAEhC,IAQO,cARD,KAAK,EAAC,WAAW;YACtB,IAAmC,cAA7B,KAAK,EAAC,YAAY,KAAC,KAAG;YAC5B,IAKE;cAJD,KAAK,EAAC,YAAY;0BACT,WAAM;oDAAN,WAAM;cACf,WAAW,EAAC,QAAQ;cACpB,IAAI,EAAC,MAAM;;;UAIb,IAQO,cARD,KAAK,EAAC,WAAW;YACtB,IAAkC,cAA5B,KAAK,EAAC,YAAY,KAAC,IAAE;YAC3B,IAKE;cAJD,KAAK,EAAC,YAAY;0BACT,WAAM;oDAAN,WAAM;cACf,WAAW,EAAC,OAAO;cACnB,IAAI,EAAC,UAAU;;;;QAKlB,IAgCO,cAhCD,KAAK,EAAC,cAAc;UACzB,IAAuC,cAAjC,KAAK,EAAC,eAAe,KAAC,MAAI;UAEhC,IAQO,cARD,KAAK,EAAC,WAAW;YACtB,IAAmC,cAA7B,KAAK,EAAC,YAAY,KAAC,KAAG;YAC5B,IAKE;cAJD,KAAK,EAAC,YAAY;0BACT,WAAM;oDAAN,WAAM;cACf,WAAW,EAAC,SAAS;cACrB,IAAI,EAAC,MAAM;;;UAIb,IAQO,cARD,KAAK,EAAC,WAAW;YACtB,IAMO,cAND,KAAK,EAAC,UAAU;cACrB,IAAuC,cAAjC,KAAK,EAAC,YAAY,KAAC,SAAO;cAChC,IAGE;gBAFD,KAAK,EAAC,aAAa;gBACnB,OAAO,EAAE,WAAM;;;;UAKlB,IAQO,cARD,KAAK,EAAC,WAAW;YACtB,IAMO,cAND,KAAK,EAAC,UAAU;cACrB,IAAoC,cAA9B,KAAK,EAAC,YAAY,KAAC,MAAI;cAC7B,IAGE;gBAFD,KAAK,EAAC,aAAa;gBACnB,OAAO,EAAE,WAAM;;;;;QAOnB,IAGO,cAHD,KAAK,EAAC,cAAc;UACzB,IAA8D;YAAtD,KAAK,EAAC,UAAU;YAAE,OAAK,EAAE,mBAAc;cAAE,MAAI;UACrD,IAA0D;YAAlD,KAAK,EAAC,UAAU;YAAE,OAAK,EAAE,eAAU;cAAE,MAAI;;eAIf,qBAAgB;YAAnD,IAEO;;cAFD,KAAK,EAAC,gBAAgB;;cAC3B,IAA0E;gBAApE,KAAK,OAAC,aAAa,EAAS,gBAAW;sBAAI,qBAAgB"}