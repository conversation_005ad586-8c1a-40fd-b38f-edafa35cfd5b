<template>
	<view class="container">
		<!-- 顶部导航栏 -->
		<view class="header">
			<text class="header-title">电影库</text>
			<view class="header-actions">
				<button class="config-btn" @click="goToConfig">设置</button>
			</view>
		</view>

		<!-- 电影海报网格 -->
		<scroll-view class="movie-grid" scroll-y="true">
			<view class="grid-container">
				<view
					class="movie-card"
					v-for="(movie, index) in movies"
					:key="index"
					@click="selectMovie(movie)"
				>
					<image
						class="movie-poster"
						:src="movie.poster"
						mode="aspectFill"
					/>
					<view class="movie-info">
						<text class="movie-title">{{movie.title}}</text>
						<text class="movie-year">{{movie.year}}</text>
					</view>
				</view>
			</view>
		</scroll-view>
	</view>
</template>

<script>
	type Movie = {
		title: string
		year: string
		poster: string
	}
	export default {
		data() {
			return {
				movies: Array<Movie> = [
					{
						title: '阿凡达：水之道',
						year: '2022',
						poster: 'https://img.omdbapi.com/?i=tt1630029&h=600&apikey=placeholder'
					},
					{
						title: '复仇者联盟：终局之战',
						year: '2019',
						poster: 'https://img.omdbapi.com/?i=tt4154796&h=600&apikey=placeholder'
					},
					{
						title: '蜘蛛侠：英雄无归',
						year: '2021',
						poster: 'https://img.omdbapi.com/?i=tt10872600&h=600&apikey=placeholder'
					},
					{
						title: '黑豹',
						year: '2018',
						poster: 'https://img.omdbapi.com/?i=tt1825683&h=600&apikey=placeholder'
					},
					{
						title: '奇异博士2：疯狂多元宇宙',
						year: '2022',
						poster: 'https://img.omdbapi.com/?i=tt9419884&h=600&apikey=placeholder'
					},
					{
						title: '雷神4：爱与雷电',
						year: '2022',
						poster: 'https://img.omdbapi.com/?i=tt10648342&h=600&apikey=placeholder'
					}
				]
			}
		},
		onLoad() {
			// 页面加载时的初始化逻辑
		},
		methods: {
			selectMovie(movie: Movie) {
				uni.showToast({
					title: `选择了: ${movie.title}`,
					duration: 2000
				})
			},
			goToConfig() {
				uni.navigateTo({
					url: '/pages/config/config'
				})
			}
		}
	}
</script>

<style>
	.container {
		flex: 1;
		background-color: #1a1a1a;
		min-height: 100vh;
	}

	.header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 20px;
		background-color: #2d2d2d;
		border-bottom: 1px solid #404040;
	}

	.header-title {
		font-size: 24px;
		font-weight: bold;
		color: #ffffff;
	}

	.header-actions {
		display: flex;
		align-items: center;
	}

	.config-btn {
		background-color: #007AFF;
		color: #ffffff;
		border: none;
		border-radius: 8px;
		padding: 8px 16px;
		font-size: 14px;
		cursor: pointer;
	}

	.config-btn:hover {
		background-color: #0056CC;
	}

	.movie-grid {
		flex: 1;
		padding: 20px;
	}

	.grid-container {
		display: flex;
		flex-wrap: wrap;
		gap: 20px;
		justify-content: space-between;
	}

	.movie-card {
		width: calc(50% - 10px);
		background-color: #2d2d2d;
		border-radius: 12px;
		overflow: hidden;
		box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
		transition: transform 0.2s ease;
		cursor: pointer;
	}

	.movie-card:hover {
		transform: translateY(-4px);
		box-shadow: 0 8px 16px rgba(0, 0, 0, 0.4);
	}

	.movie-poster {
		width: 100%;
		height: 240px;
		background-color: #404040;
	}

	.movie-info {
		padding: 12px;
	}

	.movie-title {
		font-size: 16px;
		font-weight: bold;
		color: #ffffff;
		margin-bottom: 4px;
		display: block;
		overflow: hidden;
		text-overflow: ellipsis;
		white-space: nowrap;
	}

	.movie-year {
		font-size: 14px;
		color: #999999;
		display: block;
	}

	/* 响应式设计 - 大屏幕显示更多列 */
	@media (min-width: 768px) {
		.movie-card {
			width: calc(33.333% - 14px);
		}
	}

	@media (min-width: 1024px) {
		.movie-card {
			width: calc(25% - 15px);
		}
	}
</style>
