{"version": 3, "sources": ["pages/index/index.uvue"], "names": [], "mappings": "AAmCC,KAAK,KAAI,GAAI;IAAA,mBAAA,CAAA,EAAA,oBAAA,CAAA,OAAA,EAAA,wBAAA,EAAA,EAAA,EAAA,CAAA,CAAA,CAAA;IACZ,KAAK,EAAE,MAAK,CAAA;IACZ,IAAI,EAAE,MAAK,CAAA;IACX,MAAM,EAAE,MAAK,CAAA;CACd,CAAA;AACA,MAAK,OAAQ,GAAE,eAAA,CAAA;IACd,IAAI;QACH,OAAO;YACN,MAAM,EAAE;gBACP;oBACC,KAAK,EAAE,SAAS;oBAChB,IAAI,EAAE,MAAM;oBACZ,MAAM,EAAE,+DAA8D;iBACtE;gBACD;oBACC,KAAK,EAAE,YAAY;oBACnB,IAAI,EAAE,MAAM;oBACZ,MAAM,EAAE,+DAA8D;iBACtE;gBACD;oBACC,KAAK,EAAE,UAAU;oBACjB,IAAI,EAAE,MAAM;oBACZ,MAAM,EAAE,gEAA+D;iBACvE;gBACD;oBACC,KAAK,EAAE,IAAI;oBACX,IAAI,EAAE,MAAM;oBACZ,MAAM,EAAE,+DAA8D;iBACtE;gBACD;oBACC,KAAK,EAAE,cAAc;oBACrB,IAAI,EAAE,MAAM;oBACZ,MAAM,EAAE,+DAA8D;iBACtE;gBACD;oBACC,KAAK,EAAE,UAAU;oBACjB,IAAI,EAAE,MAAM;oBACZ,MAAM,EAAE,gEAA+D;iBACxE;aACD,IAAK,KAAK,EAAC;SACZ,CAAA;IACD,CAAC;IACD,MAAM;QACL,cAAa;IACd,CAAC;IACD,OAAO,EAAE;QACR,WAAW,CAAC,KAAK,EAAE,KAAK;YACvB,GAAG,CAAC,SAAS,CAAC;gBACb,KAAK,EAAE,QAAQ,KAAK,CAAC,KAAK,EAAE;gBAC5B,QAAQ,EAAE,IAAG;aACb,CAAA,CAAA;QACF,CAAC;QACD,UAAU;YACT,GAAG,CAAC,UAAU,CAAC;gBACd,GAAG,EAAE,sBAAqB;aAC1B,CAAA,CAAA;QACF,CAAA;KACD;CACD,CAAA,CAAA;;;;;WA5FA,GAAA,CA8BO,MAAA,EAAA,GAAA,CAAA,EA9BD,KAAK,EAAC,WAAW,EAAA,CAAA,EAAA;QAEtB,GAAA,CAKO,MAAA,EAAA,GAAA,CAAA,EALD,KAAK,EAAC,QAAQ,EAAA,CAAA,EAAA;YACnB,GAAA,CAAqC,MAAA,EAAA,GAAA,CAAA,EAA/B,KAAK,EAAC,cAAc,EAAA,CAAA,EAAC,KAAG,CAAA;YAC9B,GAAA,CAEO,MAAA,EAAA,GAAA,CAAA,EAFD,KAAK,EAAC,gBAAgB,EAAA,CAAA,EAAA;gBAC3B,GAAA,CAA0D,QAAA,EAAA,GAAA,CAAA;oBAAlD,KAAK,EAAC,YAAY;oBAAE,OAAK,EAAE,IAAA,CAAA,UAAU;oBAAE,IAAE,EAAA,CAAA,CAAA,WAAA,EAAA,CAAA,SAAA,CAAA,CAAA;;;QAKnD,GAAA,CAmBc,aAAA,EAAA,GAAA,CAAA;YAnBD,KAAK,EAAC,YAAY;YAAC,UAAQ,EAAC,MAAM;;YAC9C,GAAA,CAiBO,MAAA,EAAA,GAAA,CAAA,EAjBD,KAAK,EAAC,gBAAgB,EAAA,CAAA,EAAA;gBAC3B,GAAA,CAeO,QAAA,EAAA,IAAA,EAAA,aAAA,CAAA,UAAA,CAbmB,IAAA,CAAA,MAAM,EAAA,CAAvB,KAAK,EAAE,KAAK,EAAZ,OAAK,EAAA,OAAA,GAAA,GAAA,CAAA,EAAA;2BAFd,GAAA,CAeO,MAAA,EAAA,GAAA,CAAA;wBAdN,KAAK,EAAC,YAAY;wBAEjB,GAAG,EAAE,KAAK;wBACV,OAAK,EAAA,GAAA,EAAA,GAAE,IAAA,CAAA,WAAW,CAAC,KAAK,CAAA,CAAA,CAAA,CAAA;;wBAEzB,GAAA,CAIE,OAAA,EAAA,GAAA,CAAA;4BAHD,KAAK,EAAC,cAAc;4BACnB,GAAG,EAAE,KAAK,CAAC,MAAM;4BAClB,IAAI,EAAC,YAAY;;wBAElB,GAAA,CAGO,MAAA,EAAA,GAAA,CAAA,EAHD,KAAK,EAAC,YAAY,EAAA,CAAA,EAAA;4BACvB,GAAA,CAAgD,MAAA,EAAA,GAAA,CAAA,EAA1C,KAAK,EAAC,aAAa,EAAA,CAAA,EAAA,GAAA,CAAG,KAAK,CAAC,KAAK,CAAA,EAAA,CAAA,CAAA,UAAA,CAAA;4BACvC,GAAA,CAA8C,MAAA,EAAA,GAAA,CAAA,EAAxC,KAAK,EAAC,YAAY,EAAA,CAAA,EAAA,GAAA,CAAG,KAAK,CAAC,IAAI,CAAA,EAAA,CAAA,CAAA,UAAA,CAAA", "file": "pages/index/index.uvue", "sourcesContent": ["<template>\r\n\t<view class=\"container\">\r\n\t\t<!-- 顶部导航栏 -->\r\n\t\t<view class=\"header\">\r\n\t\t\t<text class=\"header-title\">电影库</text>\r\n\t\t\t<view class=\"header-actions\">\r\n\t\t\t\t<button class=\"config-btn\" @click=\"goToConfig\">设置</button>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\r\n\t\t<!-- 电影海报网格 -->\r\n\t\t<scroll-view class=\"movie-grid\" scroll-y=\"true\">\r\n\t\t\t<view class=\"grid-container\">\r\n\t\t\t\t<view\n\t\t\t\t\tclass=\"movie-card\"\n\t\t\t\t\tv-for=\"(movie, index) in movies\"\n\t\t\t\t\t:key=\"index\"\n\t\t\t\t\t@click=\"selectMovie(movie)\"\n\t\t\t\t>\n\t\t\t\t\t<image\n\t\t\t\t\t\tclass=\"movie-poster\"\n\t\t\t\t\t\t:src=\"movie.poster\"\n\t\t\t\t\t\tmode=\"aspectFill\"\n\t\t\t\t\t/>\n\t\t\t\t\t<view class=\"movie-info\">\n\t\t\t\t\t\t<text class=\"movie-title\">{{movie.title}}</text>\n\t\t\t\t\t\t<text class=\"movie-year\">{{movie.year}}</text>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</scroll-view>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\ttype Movie = {\n\t\ttitle: string\n\t\tyear: string\n\t\tposter: string\n\t}\n\texport default {\n\t\tdata() {\n\t\t\treturn {\n\t\t\t\tmovies: [\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\ttitle: '阿凡达：水之道',\r\n\t\t\t\t\t\tyear: '2022',\r\n\t\t\t\t\t\tposter: 'https://img.omdbapi.com/?i=tt1630029&h=600&apikey=placeholder'\r\n\t\t\t\t\t},\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\ttitle: '复仇者联盟：终局之战',\r\n\t\t\t\t\t\tyear: '2019',\r\n\t\t\t\t\t\tposter: 'https://img.omdbapi.com/?i=tt4154796&h=600&apikey=placeholder'\r\n\t\t\t\t\t},\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\ttitle: '蜘蛛侠：英雄无归',\r\n\t\t\t\t\t\tyear: '2021',\r\n\t\t\t\t\t\tposter: 'https://img.omdbapi.com/?i=tt10872600&h=600&apikey=placeholder'\r\n\t\t\t\t\t},\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\ttitle: '黑豹',\r\n\t\t\t\t\t\tyear: '2018',\r\n\t\t\t\t\t\tposter: 'https://img.omdbapi.com/?i=tt1825683&h=600&apikey=placeholder'\r\n\t\t\t\t\t},\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\ttitle: '奇异博士2：疯狂多元宇宙',\r\n\t\t\t\t\t\tyear: '2022',\r\n\t\t\t\t\t\tposter: 'https://img.omdbapi.com/?i=tt9419884&h=600&apikey=placeholder'\r\n\t\t\t\t\t},\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\ttitle: '雷神4：爱与雷电',\r\n\t\t\t\t\t\tyear: '2022',\r\n\t\t\t\t\t\tposter: 'https://img.omdbapi.com/?i=tt10648342&h=600&apikey=placeholder'\r\n\t\t\t\t\t}\r\n\t\t\t\t] as Movie[]\r\n\t\t\t}\r\n\t\t},\r\n\t\tonLoad() {\r\n\t\t\t// 页面加载时的初始化逻辑\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\tselectMovie(movie: Movie) {\r\n\t\t\t\tuni.showToast({\r\n\t\t\t\t\ttitle: `选择了: ${movie.title}`,\r\n\t\t\t\t\tduration: 2000\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\tgoToConfig() {\r\n\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\turl: '/pages/config/config'\r\n\t\t\t\t})\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style>\r\n\t.container {\r\n\t\tflex: 1;\r\n\t\tbackground-color: #1a1a1a;\r\n\t\tmin-height: 100vh;\r\n\t}\r\n\r\n\t.header {\r\n\t\tdisplay: flex;\r\n\t\tjustify-content: space-between;\r\n\t\talign-items: center;\r\n\t\tpadding: 20px;\r\n\t\tbackground-color: #2d2d2d;\r\n\t\tborder-bottom: 1px solid #404040;\r\n\t}\r\n\r\n\t.header-title {\r\n\t\tfont-size: 24px;\r\n\t\tfont-weight: bold;\r\n\t\tcolor: #ffffff;\r\n\t}\r\n\r\n\t.header-actions {\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t}\r\n\r\n\t.config-btn {\r\n\t\tbackground-color: #007AFF;\r\n\t\tcolor: #ffffff;\r\n\t\tborder: none;\r\n\t\tborder-radius: 8px;\r\n\t\tpadding: 8px 16px;\r\n\t\tfont-size: 14px;\r\n\t\tcursor: pointer;\r\n\t}\r\n\r\n\t.config-btn:hover {\r\n\t\tbackground-color: #0056CC;\r\n\t}\r\n\r\n\t.movie-grid {\r\n\t\tflex: 1;\r\n\t\tpadding: 20px;\r\n\t}\r\n\r\n\t.grid-container {\r\n\t\tdisplay: flex;\r\n\t\tflex-wrap: wrap;\r\n\t\tgap: 20px;\r\n\t\tjustify-content: space-between;\r\n\t}\r\n\r\n\t.movie-card {\r\n\t\twidth: calc(50% - 10px);\r\n\t\tbackground-color: #2d2d2d;\r\n\t\tborder-radius: 12px;\r\n\t\toverflow: hidden;\r\n\t\tbox-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);\r\n\t\ttransition: transform 0.2s ease;\r\n\t\tcursor: pointer;\r\n\t}\r\n\r\n\t.movie-card:hover {\r\n\t\ttransform: translateY(-4px);\r\n\t\tbox-shadow: 0 8px 16px rgba(0, 0, 0, 0.4);\r\n\t}\r\n\r\n\t.movie-poster {\r\n\t\twidth: 100%;\r\n\t\theight: 240px;\r\n\t\tbackground-color: #404040;\r\n\t}\r\n\r\n\t.movie-info {\r\n\t\tpadding: 12px;\r\n\t}\r\n\r\n\t.movie-title {\r\n\t\tfont-size: 16px;\r\n\t\tfont-weight: bold;\r\n\t\tcolor: #ffffff;\r\n\t\tmargin-bottom: 4px;\r\n\t\tdisplay: block;\r\n\t\toverflow: hidden;\r\n\t\ttext-overflow: ellipsis;\r\n\t\twhite-space: nowrap;\r\n\t}\r\n\r\n\t.movie-year {\r\n\t\tfont-size: 14px;\r\n\t\tcolor: #999999;\r\n\t\tdisplay: block;\r\n\t}\r\n\r\n\t/* 响应式设计 - 大屏幕显示更多列 */\r\n\t@media (min-width: 768px) {\r\n\t\t.movie-card {\r\n\t\t\twidth: calc(33.333% - 14px);\r\n\t\t}\r\n\t}\r\n\r\n\t@media (min-width: 1024px) {\r\n\t\t.movie-card {\r\n\t\t\twidth: calc(25% - 15px);\r\n\t\t}\r\n\t}\r\n</style>\r\n"]}