{"version": 3, "sources": ["pages/index/index.uvue"], "names": [], "mappings": "AAQC,MAAK,OAAQ,GAAE,eAAA,CAAA;IACd,IAAI;QACH,OAAO;YACN,KAAK,EAAE,OAAM;SACd,CAAA;IACD,CAAC;IACD,MAAM;IAEN,CAAC;IACD,OAAO,EAAE,EAET;CACD,CAAA,CAAA;;;;;WAnBA,GAAA,CAGO,MAAA,EAAA,IAAA,EAAA;QAFN,GAAA,CAAmD,OAAA,EAAA,GAAA,CAAA;YAA5C,KAAK,EAAC,MAAM;YAAC,GAAG,EAAC,kBAAkB;;QAC1C,GAAA,CAAoC,MAAA,EAAA,GAAA,CAAA,EAA9B,KAAK,EAAC,OAAO,EAAA,CAAA,EAAA,GAAA,CAAG,IAAA,CAAA,KAAK,CAAA,EAAA,CAAA,CAAA,UAAA,CAAA", "file": "pages/index/index.uvue", "sourcesContent": ["<template>\r\n\t<view>\r\n\t\t<image class=\"logo\" src=\"/static/logo.png\"></image>\r\n\t\t<text class=\"title\">{{title}}</text>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\texport default {\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\ttitle: 'Hello'\r\n\t\t\t}\r\n\t\t},\r\n\t\tonLoad() {\r\n\r\n\t\t},\r\n\t\tmethods: {\r\n\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style>\r\n\t.logo {\r\n\t\theight: 100px;\r\n\t\twidth: 100px;\r\n\t\tmargin: 100px auto 25px auto;\r\n\t}\r\n\r\n\t.title {\r\n\t\tfont-size: 18px;\r\n\t\tcolor: #8f8f94;\r\n    text-align: center;\r\n\t}\r\n</style>\r\n"]}