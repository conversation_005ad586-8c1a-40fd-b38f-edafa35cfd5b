{"version": 3, "sources": ["D:/HBuilderX/plugins/uniapp-cli-vite/node_modules/@dcloudio/uni-console/src/runtime/app/socket.ts", "App.uvue", "D:/HBuilderX/plugins/uniapp-cli-vite/node_modules/@dcloudio/uni-console/src/runtime/app/index.ts", "pages/index/index.uvue", "main.uts"], "sourcesContent": ["/// <reference types=\"@dcloudio/uni-app-x/types/uni/global\" />\n// 之所以又写了一份，是因为外层的socket，connectSocket的时候必须传入multiple:true\n// 但是android又不能传入，目前代码里又不能写条件编译之类的。\nexport function initRuntimeSocket(\n  hosts: string,\n  port: string,\n  id: string\n): Promise<SocketTask | null> {\n  if (hosts == '' || port == '' || id == '') return Promise.resolve(null)\n  return hosts\n    .split(',')\n    .reduce<Promise<SocketTask | null>>(\n      (\n        promise: Promise<SocketTask | null>,\n        host: string\n      ): Promise<SocketTask | null> => {\n        return promise.then((socket): Promise<SocketTask | null> => {\n          if (socket != null) return Promise.resolve(socket)\n          return tryConnectSocket(host, port, id)\n        })\n      },\n      Promise.resolve(null)\n    )\n}\n\nconst SOCKET_TIMEOUT = 500\nfunction tryConnectSocket(\n  host: string,\n  port: string,\n  id: string\n): Promise<SocketTask | null> {\n  return new Promise((resolve, reject) => {\n    const socket = uni.connectSocket({\n      url: `ws://${host}:${port}/${id}`,\n      fail() {\n        resolve(null)\n      },\n    })\n    const timer = setTimeout(() => {\n      // @ts-expect-error\n      socket.close({\n        code: 1006,\n        reason: 'connect timeout',\n      } as CloseSocketOptions)\n      resolve(null)\n    }, SOCKET_TIMEOUT)\n\n    socket.onOpen((e) => {\n      clearTimeout(timer)\n      resolve(socket)\n    })\n    socket.onClose((e) => {\n      clearTimeout(timer)\n      resolve(null)\n    })\n    socket.onError((e) => {\n      clearTimeout(timer)\n      resolve(null)\n    })\n  })\n}\n", "<script lang=\"uts\">\r\n\r\n\tlet firstBackTime = 0\r\n\r\n\texport default {\r\n\t\tonLaunch: function () {\r\n\t\t\tconsole.log('App Launch')\r\n\t\t},\r\n\t\tonShow: function () {\r\n\t\t\tconsole.log('App Show')\r\n\t\t},\r\n\t\tonHide: function () {\r\n\t\t\tconsole.log('App Hide')\r\n\t\t},\r\n\r\n\t\tonLastPageBackPress: function () {\r\n\t\t\tconsole.log('App LastPageBackPress')\r\n\t\t\tif (firstBackTime == 0) {\r\n\t\t\t\tuni.showToast({\r\n\t\t\t\t\ttitle: '再按一次退出应用',\r\n\t\t\t\t\tposition: 'bottom',\r\n\t\t\t\t})\r\n\t\t\t\tfirstBackTime = Date.now()\r\n\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\tfirstBackTime = 0\r\n\t\t\t\t}, 2000)\r\n\t\t\t} else if (Date.now() - firstBackTime < 2000) {\r\n\t\t\t\tfirstBackTime = Date.now()\r\n\t\t\t\tuni.exit()\r\n\t\t\t}\r\n\t\t},\r\n\r\n\t\tonExit: function () {\r\n\t\t\tconsole.log('App Exit')\r\n\t\t},\r\n\t}\r\n</script>\r\n\r\n<style>\r\n\t/*每个页面公共css */\r\n\t.uni-row {\r\n\t\tflex-direction: row;\r\n\t}\r\n\r\n\t.uni-column {\r\n\t\tflex-direction: column;\r\n\t}\r\n</style>", "import { initRuntimeSocket } from './socket'\n\nexport function initRuntimeSocketService(): Promise<boolean> {\n  const hosts: string = process.env.UNI_SOCKET_HOSTS\n  const port: string = process.env.UNI_SOCKET_PORT\n  const id: string = process.env.UNI_SOCKET_ID\n  if (hosts == '' || port == '' || id == '') return Promise.resolve(false)\n  let socketTask: SocketTask | null = null\n  __registerWebViewUniConsole(\n    (): string => {\n      return process.env.UNI_CONSOLE_WEBVIEW_EVAL_JS_CODE\n    },\n    (data: string) => {\n      socketTask?.send({\n        data,\n      } as SendSocketMessageOptions)\n    }\n  )\n  return Promise.resolve()\n    .then((): Promise<boolean> => {\n      return initRuntimeSocket(hosts, port, id).then((socket): boolean => {\n        if (socket == null) {\n          return false\n        }\n        socketTask = socket\n        return true\n      })\n    })\n    .catch((): boolean => {\n      return false\n    })\n}\n\ninitRuntimeSocketService()\n", "<template>\r\n\t<view class=\"container\">\r\n\t\t<!-- 顶部导航栏 -->\r\n\t\t<view class=\"header\">\r\n\t\t\t<text class=\"header-title\">电影库</text>\r\n\t\t\t<view class=\"header-actions\">\r\n\t\t\t\t<button class=\"config-btn\" @click=\"goToConfig\">设置</button>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\r\n\t\t<!-- 电影海报网格 -->\r\n\t\t<scroll-view class=\"movie-grid\" scroll-y=\"true\">\r\n\t\t\t<view class=\"grid-container\">\r\n\t\t\t\t<view\n\t\t\t\t\tclass=\"movie-card\"\n\t\t\t\t\tv-for=\"(movie, index) in movies\"\n\t\t\t\t\t:key=\"index\"\n\t\t\t\t\t@click=\"selectMovie(movie)\"\n\t\t\t\t>\n\t\t\t\t\t<image\n\t\t\t\t\t\tclass=\"movie-poster\"\n\t\t\t\t\t\t:src=\"movie.poster\"\n\t\t\t\t\t\tmode=\"aspectFill\"\n\t\t\t\t\t/>\n\t\t\t\t\t<view class=\"movie-info\">\n\t\t\t\t\t\t<text class=\"movie-title\">{{movie.title}}</text>\n\t\t\t\t\t\t<text class=\"movie-year\">{{movie.year}}</text>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</scroll-view>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\ttype Movie = {\n\t\ttitle: string\n\t\tyear: string\n\t\tposter: string\n\t}\n\texport default {\n\t\tdata() {\n\t\t\treturn {\n\t\t\t\tmovies: [\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\ttitle: '阿凡达：水之道',\r\n\t\t\t\t\t\tyear: '2022',\r\n\t\t\t\t\t\tposter: 'https://img.omdbapi.com/?i=tt1630029&h=600&apikey=placeholder'\r\n\t\t\t\t\t},\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\ttitle: '复仇者联盟：终局之战',\r\n\t\t\t\t\t\tyear: '2019',\r\n\t\t\t\t\t\tposter: 'https://img.omdbapi.com/?i=tt4154796&h=600&apikey=placeholder'\r\n\t\t\t\t\t},\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\ttitle: '蜘蛛侠：英雄无归',\r\n\t\t\t\t\t\tyear: '2021',\r\n\t\t\t\t\t\tposter: 'https://img.omdbapi.com/?i=tt10872600&h=600&apikey=placeholder'\r\n\t\t\t\t\t},\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\ttitle: '黑豹',\r\n\t\t\t\t\t\tyear: '2018',\r\n\t\t\t\t\t\tposter: 'https://img.omdbapi.com/?i=tt1825683&h=600&apikey=placeholder'\r\n\t\t\t\t\t},\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\ttitle: '奇异博士2：疯狂多元宇宙',\r\n\t\t\t\t\t\tyear: '2022',\r\n\t\t\t\t\t\tposter: 'https://img.omdbapi.com/?i=tt9419884&h=600&apikey=placeholder'\r\n\t\t\t\t\t},\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\ttitle: '雷神4：爱与雷电',\r\n\t\t\t\t\t\tyear: '2022',\r\n\t\t\t\t\t\tposter: 'https://img.omdbapi.com/?i=tt10648342&h=600&apikey=placeholder'\r\n\t\t\t\t\t}\r\n\t\t\t\t] as Movie[]\r\n\t\t\t}\r\n\t\t},\r\n\t\tonLoad() {\r\n\t\t\t// 页面加载时的初始化逻辑\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\tselectMovie(movie: Movie) {\r\n\t\t\t\tuni.showToast({\r\n\t\t\t\t\ttitle: `选择了: ${movie.title}`,\r\n\t\t\t\t\tduration: 2000\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\tgoToConfig() {\r\n\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\turl: '/pages/config/config'\r\n\t\t\t\t})\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style>\r\n\t.container {\r\n\t\tflex: 1;\r\n\t\tbackground-color: #1a1a1a;\r\n\t\tmin-height: 100vh;\r\n\t}\r\n\r\n\t.header {\r\n\t\tdisplay: flex;\r\n\t\tjustify-content: space-between;\r\n\t\talign-items: center;\r\n\t\tpadding: 20px;\r\n\t\tbackground-color: #2d2d2d;\r\n\t\tborder-bottom: 1px solid #404040;\r\n\t}\r\n\r\n\t.header-title {\r\n\t\tfont-size: 24px;\r\n\t\tfont-weight: bold;\r\n\t\tcolor: #ffffff;\r\n\t}\r\n\r\n\t.header-actions {\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t}\r\n\r\n\t.config-btn {\r\n\t\tbackground-color: #007AFF;\r\n\t\tcolor: #ffffff;\r\n\t\tborder: none;\r\n\t\tborder-radius: 8px;\r\n\t\tpadding: 8px 16px;\r\n\t\tfont-size: 14px;\r\n\t\tcursor: pointer;\r\n\t}\r\n\r\n\t.config-btn:hover {\r\n\t\tbackground-color: #0056CC;\r\n\t}\r\n\r\n\t.movie-grid {\r\n\t\tflex: 1;\r\n\t\tpadding: 20px;\r\n\t}\r\n\r\n\t.grid-container {\r\n\t\tdisplay: flex;\r\n\t\tflex-wrap: wrap;\r\n\t\tgap: 20px;\r\n\t\tjustify-content: space-between;\r\n\t}\r\n\r\n\t.movie-card {\r\n\t\twidth: calc(50% - 10px);\r\n\t\tbackground-color: #2d2d2d;\r\n\t\tborder-radius: 12px;\r\n\t\toverflow: hidden;\r\n\t\tbox-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);\r\n\t\ttransition: transform 0.2s ease;\r\n\t\tcursor: pointer;\r\n\t}\r\n\r\n\t.movie-card:hover {\r\n\t\ttransform: translateY(-4px);\r\n\t\tbox-shadow: 0 8px 16px rgba(0, 0, 0, 0.4);\r\n\t}\r\n\r\n\t.movie-poster {\r\n\t\twidth: 100%;\r\n\t\theight: 240px;\r\n\t\tbackground-color: #404040;\r\n\t}\r\n\r\n\t.movie-info {\r\n\t\tpadding: 12px;\r\n\t}\r\n\r\n\t.movie-title {\r\n\t\tfont-size: 16px;\r\n\t\tfont-weight: bold;\r\n\t\tcolor: #ffffff;\r\n\t\tmargin-bottom: 4px;\r\n\t\tdisplay: block;\r\n\t\toverflow: hidden;\r\n\t\ttext-overflow: ellipsis;\r\n\t\twhite-space: nowrap;\r\n\t}\r\n\r\n\t.movie-year {\r\n\t\tfont-size: 14px;\r\n\t\tcolor: #999999;\r\n\t\tdisplay: block;\r\n\t}\r\n\r\n\t/* 响应式设计 - 大屏幕显示更多列 */\r\n\t@media (min-width: 768px) {\r\n\t\t.movie-card {\r\n\t\t\twidth: calc(33.333% - 14px);\r\n\t\t}\r\n\t}\r\n\r\n\t@media (min-width: 1024px) {\r\n\t\t.movie-card {\r\n\t\t\twidth: calc(25% - 15px);\r\n\t\t}\r\n\t}\r\n</style>\r\n", "import 'D:/HBuilderX/plugins/uniapp-cli-vite/node_modules/@dcloudio/uni-console/src/runtime/app/index.ts';import App from './App.uvue'\r\n\r\nimport { createSSRApp } from 'vue'\r\nexport function createApp() {\r\n\tconst app = createSSRApp(App)\r\n\treturn {\r\n\t\tapp\r\n\t}\r\n}\nexport function main(app: IApp) {\n    definePageRoutes();\n    defineAppConfig();\n    (createApp()['app'] as VueApp).mount(app, GenUniApp());\n}\n\nexport class UniAppConfig extends io.dcloud.uniapp.appframe.AppConfig {\n    override name: string = \"test-uniapp-x\"\n    override appid: string = \"__UNI__666077E\"\n    override versionName: string = \"1.0.0\"\n    override versionCode: string = \"100\"\n    override uniCompilerVersion: string = \"4.76\"\n    \n    constructor() { super() }\n}\n\nimport GenPagesIndexIndexClass from './pages/index/index.uvue'\nimport GenPagesConfigConfigClass from './pages/config/config.uvue'\nfunction definePageRoutes() {\n__uniRoutes.push({ path: \"pages/index/index\", component: GenPagesIndexIndexClass, meta: { isQuit: true } as UniPageMeta, style: _uM([[\"navigationBarTitleText\",\"电影库\"],[\"navigationBarBackgroundColor\",\"#2d2d2d\"],[\"navigationBarTextStyle\",\"white\"],[\"backgroundColor\",\"#1a1a1a\"]]) } as UniPageRoute)\n__uniRoutes.push({ path: \"pages/config/config\", component: GenPagesConfigConfigClass, meta: { isQuit: false } as UniPageMeta, style: _uM([[\"navigationBarTitleText\",\"WebDAV配置\"],[\"navigationBarBackgroundColor\",\"#2d2d2d\"],[\"navigationBarTextStyle\",\"white\"],[\"backgroundColor\",\"#1a1a1a\"]]) } as UniPageRoute)\n}\nconst __uniTabBar: Map<string, any | null> | null = null\nconst __uniLaunchPage: Map<string, any | null> = _uM([[\"url\",\"pages/index/index\"],[\"style\",_uM([[\"navigationBarTitleText\",\"电影库\"],[\"navigationBarBackgroundColor\",\"#2d2d2d\"],[\"navigationBarTextStyle\",\"white\"],[\"backgroundColor\",\"#1a1a1a\"]])]])\nfunction defineAppConfig(){\n  __uniConfig.entryPagePath = '/pages/index/index'\n  __uniConfig.globalStyle = _uM([[\"navigationBarTextStyle\",\"black\"],[\"navigationBarTitleText\",\"uni-app x\"],[\"navigationBarBackgroundColor\",\"#F8F8F8\"],[\"backgroundColor\",\"#F8F8F8\"]])\n  __uniConfig.getTabBarConfig = ():Map<string, any> | null =>  null\n  __uniConfig.tabBar = __uniConfig.getTabBarConfig()\n  __uniConfig.conditionUrl = ''\n  __uniConfig.uniIdRouter = _uM()\n  \n  __uniConfig.ready = true\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;+BAgCuB;+BCJf;+BAVA;;;;;;ADfD,IAAS,kBACd,OAAO,MAAM,EACb,MAAM,MAAM,EACZ,IAAI,MAAM,GACT,WAAQ,aAAmB;IAC5B,IAAI,SAAS,MAAM,QAAQ,MAAM,MAAM;QAAI,OAAO,WAAQ,OAAO,CAAC,IAAI;;IACtE,OAAO,MACJ,KAAK,CAAC,KACN,MAAM,CAAC,WAAQ,cACd,IACE,SAAS,WAAQ,cACjB,MAAM,MAAM,GACX,WAAQ,aAAsB;QAC/B,OAAO,QAAQ,IAAI,CAAC,IAAC,SAAS,WAAQ,aAAsB;YAC1D,IAAI,UAAU,IAAI;gBAAE,OAAO,WAAQ,OAAO,CAAC;;YAC3C,OAAO,iBAAiB,MAAM,MAAM;QACtC;;IACF;MACA,WAAQ,OAAO,CAAC,IAAI;AAE1B;AAEA,IAAM,yBAAiB,GAAG;AAC1B,IAAS,iBACP,MAAM,MAAM,EACZ,MAAM,MAAM,EACZ,IAAI,MAAM,GACT,WAAQ,aAAmB;IAC5B,OAAO,AAAI,WAAQ,IAAC,SAAS,OAAW;QACtC,IAAM,SAAS,uCACb,MAAK,AAAC,UAAO,OAAK,MAAG,OAAK,MAAG,IAC7B,OAAA,OAAO;YACL,QAAQ,IAAI;QACd;;QAEF,IAAM,QAAQ,WAAW,KAAM;YAE7B,OAAO,KAAK,oBACV,OAAM,IAAI,EACV,SAAQ;YAEV,QAAQ,IAAI;QACd;UAAG;QAEH,OAAO,MAAM,CAAC,IAAC,EAAM;YACnB,aAAa;YACb,QAAQ;QACV;;QACA,OAAO,OAAO,CAAC,IAAC,EAAM;YACpB,aAAa;YACb,QAAQ,IAAI;QACd;;QACA,OAAO,OAAO,CAAC,IAAC,EAAM;YACpB,aAAa;YACb,QAAQ,IAAI;QACd;;IACF;;AACF;AE1DO,IAAS,4BAA4B,WAAQ,OAAO,EAAE;IAC3D,IAAM,OAAO,MAAM;IACnB,IAAM,MAAM,MAAM;IAClB,IAAM,IAAI,MAAM;IAChB,IAAI,SAAS,MAAM,QAAQ,MAAM,MAAM;QAAI,OAAO,WAAQ,OAAO,CAAC,KAAK;;IACvE,IAAI,YAAY,cAAoB,IAAI;IACxC,4BACE,OAAI,MAAM,CAAI;QACZ;IACF;MACA,IAAC,MAAM,MAAM,CAAK;QAChB,YAAY,8BACV,OAAA;IAEJ;;IAEF,OAAO,WAAQ,OAAO,GACnB,IAAI,CAAC,OAAI,WAAQ,OAAO,EAAK;QAC5B,OAAO,kBAAkB,OAAO,MAAM,IAAI,IAAI,CAAC,IAAC,SAAS,OAAO,CAAI;YAClE,IAAI,UAAU,IAAI,EAAE;gBAClB,OAAO,KAAK;YACd;YACA,aAAa;YACb,OAAO,IAAI;QACb;;IACF;MACC,OAAK,CAAC,OAAI,OAAO,CAAI;QACpB,OAAO,KAAK;IACd;;AACJ;;IAEA;;AD/BC,IAAI,wBAAgB,CAAA;AAEf;;iBACM,wBAAA;YACT,QAAQ,GAAG,CAAC,cAAY;QACzB;;kBACQ,sBAAA;YACP,QAAQ,GAAG,CAAC,YAAU;QACvB;;kBACQ,MAAA;YACP,QAAQ,GAAG,CAAC,YAAU;QACvB;;4BAEqB,MAAA;YACpB,QAAQ,GAAG,CAAC,yBAAuB;YACnC,IAAI,iBAAiB,CAAC,EAAE;gBACvB,+BACC,QAAO,YACP,WAAU;gBAEX,gBAAgB,KAAK,GAAG;gBACxB,WAAW,KAAI;oBACd,gBAAgB,CAAA;gBACjB,GAAG,IAAI;mBACD,IAAI,KAAK,GAAG,KAAK,gBAAgB,IAAI,EAAE;gBAC7C,gBAAgB,KAAK,GAAG;gBACxB;;QAEF;;eAEQ,MAAA;YACP,QAAQ,GAAG,CAAC,YAAU;QACvB;;;;;;;;;;;;;;AACD;;;;;;;;AEAa,WAAR;IACJ;oBAAO,MAAK,CAAA;IACZ;mBAAM,MAAK,CAAA;IACX;qBAAQ,MAAK,CAAA;;;oCAHD,SAAA,0BAAA,EAAA,EAAA,CAAA;;;;;;iCAAR,oBAAA;;;;;4GACJ,gBAAA,OACA,eAAA,MACA,iBAAA;;;;;;;;;iBAFA,OAAO,MAAK;;kDAAZ;;;;;;mCAAA;oBAAA;;;iBACA,MAAM,MAAK;;iDAAX;;;;;;mCAAA;oBAAA;;;iBACA,QAAQ,MAAK;;mDAAb;;;;;;mCAAA;oBAAA;;;;;;;;;;;;;;;;;;ACnCI,IAAU,aAAS,cAAA;IACxB,IAAM,MAAM;IACZ,OAAO,IACN,SAAA;AAEF;AACM,IAAU,KAAK,KAAK,IAAI,EAAA;IAC1B;IACA;IACA,CAAC,WAAW,CAAC,MAAM,CAAA,EAAA,CAAI,MAAM,EAAE,KAAK,CAAC,KAAK;AAC9C;AAEM,WAAO,eAAqB,GAAG,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,SAAS;IACjE,aAAS,MAAM,MAAM,GAAG,eAAe;IACvC,aAAS,OAAO,MAAM,GAAG,gBAAgB;IACzC,aAAS,aAAa,MAAM,GAAG,OAAO;IACtC,aAAS,aAAa,MAAM,GAAG,KAAK;IACpC,aAAS,oBAAoB,MAAM,GAAG,MAAM;IAE5C,gBAAgB,KAAK,GAArB,CAAwB;;AAK5B,IAAS,mBAAgB;IACzB,YAAY,IAAI,cAAG,OAAM,qBAAqB,qCAAoC,mBAAQ,SAAQ,IAAI,GAAmB,QAAO,IAAM,4BAAyB,OAAQ,kCAA+B,WAAY,4BAAyB,SAAU,qBAAkB;IACvQ,YAAY,IAAI,cAAG,OAAM,uBAAuB,uCAAsC,mBAAQ,SAAQ,KAAK,GAAmB,QAAO,IAAM,4BAAyB,YAAa,kCAA+B,WAAY,4BAAyB,SAAU,qBAAkB;AACjR;AAEA,IAAM,iBAAiB,IAAI,MAAM,EAAE,GAAG,KAAW,IAAM,SAAM,qBAAsB,WAAQ,IAAM,4BAAyB,OAAQ,kCAA+B,WAAY,4BAAyB,SAAU,qBAAkB;AAClO,IAAS,kBAAe;IACtB,YAAY,aAAa,GAAG;IAC5B,YAAY,WAAW,GAAG,IAAM,4BAAyB,SAAU,4BAAyB,aAAc,kCAA+B,WAAY,qBAAkB;IACvK,YAAY,eAAe,GAAG,OAAG,IAAI,MAAM,EAAE,GAAG;eAAa,IAAI;;IACjE,YAAY,MAAM,GAAG,YAAY,eAAe;IAChD,YAAY,YAAY,GAAG;IAC3B,YAAY,WAAW,GAAG;IAE1B,YAAY,KAAK,GAAG,IAAI;AAC1B;;;;8BA1CA,EAAA;;;;8BAAA,EAAA;;;;uBAAA,EAAA"}