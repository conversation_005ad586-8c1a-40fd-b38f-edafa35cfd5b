{"version": 3, "sources": ["pages/config/config.uvue"], "names": [], "mappings": "AA8GC,KAAK,MAAK,GAAI;IAAA,mBAAA,CAAA,EAAA,oBAAA,CAAA,QAAA,EAAA,0BAAA,EAAA,GAAA,EAAA,CAAA,CAAA,CAAA;IACb,SAAS,EAAE,MAAK,CAAA;IAChB,IAAI,EAAE,MAAK,CAAA;IACX,QAAQ,EAAE,MAAK,CAAA;IACf,QAAQ,EAAE,MAAK,CAAA;IACf,QAAQ,EAAE,MAAK,CAAA;IACf,QAAQ,EAAE,OAAM,CAAA;IAChB,WAAW,EAAE,OAAM,CAAA;CACpB,CAAA;AACA,MAAK,OAAQ,GAAE,eAAA,CAAA;IACd,IAAI;QACH,OAAO;YACN,MAAM,EAAE;gBACP,SAAS,EAAE,EAAE;gBACb,IAAI,EAAE,KAAK;gBACX,QAAQ,EAAE,EAAE;gBACZ,QAAQ,EAAE,EAAE;gBACZ,QAAQ,EAAE,SAAS;gBACnB,QAAQ,EAAE,IAAI;gBACd,WAAW,EAAE,KAAI;aACjB;YACD,gBAAgB,EAAE,EAAE;YACpB,WAAW,EAAE,EAAC;SACf,CAAA;IACD,CAAC;IACD,MAAM;QACL,IAAI,CAAC,UAAU,EAAC,CAAA;IACjB,CAAC;IACD,OAAO,EAAE;QACR,MAAM;YACL,GAAG,CAAC,YAAY,EAAC,CAAA;QAClB,CAAC;QAED,UAAU;YACT,YAAW;YACX,IAAI;gBACH,MAAM,WAAU,GAAI,GAAG,CAAC,cAAc,CAAC,eAAe,CAAA,CAAA;gBACtD,IAAI,WAAU,IAAK,IAAI,EAAE;oBACxB,MAAM,UAAS,GAAI,WAAU,IAAK,aAAY,CAAA;oBAC9C,IAAI,CAAC,MAAM,CAAC,WAAW,CAAA,GAAI,UAAU,CAAC,WAAW,CAAA,IAAK,MAAK,IAAK,EAAC,CAAA;oBACjE,IAAI,CAAC,MAAM,CAAC,MAAM,CAAA,GAAI,UAAU,CAAC,MAAM,CAAA,IAAK,MAAK,IAAK,KAAI,CAAA;oBAC1D,IAAI,CAAC,MAAM,CAAC,UAAU,CAAA,GAAI,UAAU,CAAC,UAAU,CAAA,IAAK,MAAK,IAAK,EAAC,CAAA;oBAC/D,IAAI,CAAC,MAAM,CAAC,UAAU,CAAA,GAAI,UAAU,CAAC,UAAU,CAAA,IAAK,MAAK,IAAK,EAAC,CAAA;oBAC/D,IAAI,CAAC,MAAM,CAAC,UAAU,CAAA,GAAI,UAAU,CAAC,UAAU,CAAA,IAAK,MAAK,IAAK,SAAQ,CAAA;oBACtE,IAAI,CAAC,MAAM,CAAC,UAAU,CAAA,GAAI,UAAU,CAAC,UAAU,CAAA,IAAK,OAAM,IAAK,IAAG,CAAA;oBAClE,IAAI,CAAC,MAAM,CAAC,aAAa,CAAA,GAAI,UAAU,CAAC,aAAa,CAAA,IAAK,OAAM,IAAK,KAAI,CAAA;iBAC1E;aACD;YAAE,OAAO,CAAC,KAAA,EAAE;gBACX,OAAO,CAAC,KAAK,CAAC,CAAA,IAAK,GAAG,EAAA,kCAAA,CAAA,CAAA;aACvB;QACD,CAAC;QAED,UAAU;YACT,SAAQ;YACT,IAAI,IAAI,CAAC,MAAM,CAAC,WAAW,CAAA,IAAK,IAAG,IAAK,CAAC,IAAI,CAAC,MAAM,CAAC,WAAW,CAAA,IAAK,MAAM,CAAC,CAAC,IAAI,EAAC,IAAK,EAAE,EAAE;gBACzF,GAAG,CAAC,SAAS,CAAC;oBACb,KAAK,EAAE,UAAU;oBACjB,IAAI,EAAE,MAAK;iBACX,CAAA,CAAA;gBACD,OAAK;aACN;YAED,IAAI,IAAI,CAAC,MAAM,CAAC,UAAU,CAAA,IAAK,IAAG,IAAK,CAAC,IAAI,CAAC,MAAM,CAAC,UAAU,CAAA,IAAK,MAAM,CAAC,CAAC,IAAI,EAAC,IAAK,EAAE,EAAE;gBACvF,GAAG,CAAC,SAAS,CAAC;oBACb,KAAK,EAAE,QAAQ;oBACf,IAAI,EAAE,MAAK;iBACX,CAAA,CAAA;gBACD,OAAK;aACN;YAEA,UAAS;YACT,IAAI;gBACH,GAAG,CAAC,cAAc,CAAC,eAAe,EAAE,IAAI,CAAC,MAAM,CAAA,CAAA;gBAC/C,GAAG,CAAC,SAAS,CAAC;oBACb,KAAK,EAAE,QAAQ;oBACf,IAAI,EAAE,SAAQ;iBACd,CAAA,CAAA;aACF;YAAE,OAAO,CAAC,KAAA,EAAE;gBACX,GAAG,CAAC,SAAS,CAAC;oBACb,KAAK,EAAE,MAAM;oBACb,IAAI,EAAE,OAAM;iBACZ,CAAA,CAAA;gBACD,OAAO,CAAC,KAAK,CAAC,CAAA,IAAK,GAAG,EAAA,kCAAA,CAAA,CAAA;aACvB;QACD,CAAC;QAED,KAAI,CAAE,cAAc;YACpB,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,WAAW,CAAA,IAAK,IAAG,IAAK,CAAC,IAAI,CAAC,MAAM,CAAC,WAAW,CAAA,IAAK,MAAM,CAAC,CAAC,IAAI,EAAC,IAAK,EAAE,CAAA;gBACzF,CAAC,IAAI,CAAC,MAAM,CAAC,UAAU,CAAA,IAAK,IAAG,IAAK,CAAC,IAAI,CAAC,MAAM,CAAC,UAAU,CAAA,IAAK,MAAM,CAAC,CAAC,IAAI,EAAC,IAAK,EAAE,CAAC,EAAE;gBACtF,GAAG,CAAC,SAAS,CAAC;oBACb,KAAK,EAAE,eAAe;oBACtB,IAAI,EAAE,MAAK;iBACX,CAAA,CAAA;gBACD,OAAK;aACN;YAEA,IAAI,CAAC,gBAAe,GAAI,WAAU,CAAA;YAClC,IAAI,CAAC,WAAU,GAAI,gBAAe,CAAA;QACnC,CAAA;KACD;CACD,CAAA,CAAA;;;;;;WAjNA,GAAA,CAyGO,MAAA,EAAA,GAAA,CAAA,EAzGD,KAAK,EAAC,WAAW,EAAA,CAAA,EAAA;QAEtB,GAAA,CAIO,MAAA,EAAA,GAAA,CAAA,EAJD,KAAK,EAAC,QAAQ,EAAA,CAAA,EAAA;YACnB,GAAA,CAAoD,QAAA,EAAA,GAAA,CAAA;gBAA5C,KAAK,EAAC,UAAU;gBAAE,OAAK,EAAE,IAAA,CAAA,MAAM;gBAAE,IAAE,EAAA,CAAA,CAAA,WAAA,EAAA,CAAA,SAAA,CAAA,CAAA;YAC3C,GAAA,CAA0C,MAAA,EAAA,GAAA,CAAA,EAApC,KAAK,EAAC,cAAc,EAAA,CAAA,EAAC,UAAQ,CAAA;YACnC,GAAA,CAAwC,MAAA,EAAA,GAAA,CAAA,EAAlC,KAAK,EAAC,oBAAoB,EAAA,CAAA,CAAA;;QAIjC,GAAA,CA+Fc,aAAA,EAAA,GAAA,CAAA;YA/FD,KAAK,EAAC,gBAAgB;YAAC,UAAQ,EAAC,MAAM;;YAClD,GAAA,CA6FO,MAAA,EAAA,GAAA,CAAA,EA7FD,KAAK,EAAC,gBAAgB,EAAA,CAAA,EAAA;gBAC3B,GAAA,CAsBO,MAAA,EAAA,GAAA,CAAA,EAtBD,KAAK,EAAC,cAAc,EAAA,CAAA,EAAA;oBACzB,GAAA,CAAwC,MAAA,EAAA,GAAA,CAAA,EAAlC,KAAK,EAAC,eAAe,EAAA,CAAA,EAAC,OAAK,CAAA;oBAEjC,GAAA,CAQO,MAAA,EAAA,GAAA,CAAA,EARD,KAAK,EAAC,WAAW,EAAA,CAAA,EAAA;wBACtB,GAAA,CAAqC,MAAA,EAAA,GAAA,CAAA,EAA/B,KAAK,EAAC,YAAY,EAAA,CAAA,EAAC,OAAK,CAAA;wBAC9B,GAAA,CAKE,OAAA,EAAA,GAAA,CAAA;4BAJD,KAAK,EAAC,YAAY;wCACT,IAAA,CAAA,MAAM,CAAA,WAAA,CAAA;mEAAN,IAAA,CAAA,MAAM,CAAA,WAAA,CAAA,CAAA,GAAA,MAAA,CAAA,MAAA,CAAA,KAAA,CAAA,CAAA,CAAA;4BACf,WAAW,EAAC,gCAAgC;4BAC5C,IAAI,EAAC,KAAK;;;oBAIZ,GAAA,CAQO,MAAA,EAAA,GAAA,CAAA,EARD,KAAK,EAAC,WAAW,EAAA,CAAA,EAAA;wBACtB,GAAA,CAAmC,MAAA,EAAA,GAAA,CAAA,EAA7B,KAAK,EAAC,YAAY,EAAA,CAAA,EAAC,KAAG,CAAA;wBAC5B,GAAA,CAKE,OAAA,EAAA,GAAA,CAAA;4BAJD,KAAK,EAAC,YAAY;wCACT,IAAA,CAAA,MAAM,CAAA,MAAA,CAAA;mEAAN,IAAA,CAAA,MAAM,CAAA,MAAA,CAAA,CAAA,GAAA,MAAA,CAAA,MAAA,CAAA,KAAA,CAAA,CAAA,CAAA;4BACf,WAAW,EAAC,KAAK;4BACjB,IAAI,EAAC,QAAQ;;;;gBAKhB,GAAA,CAsBO,MAAA,EAAA,GAAA,CAAA,EAtBD,KAAK,EAAC,cAAc,EAAA,CAAA,EAAA;oBACzB,GAAA,CAAuC,MAAA,EAAA,GAAA,CAAA,EAAjC,KAAK,EAAC,eAAe,EAAA,CAAA,EAAC,MAAI,CAAA;oBAEhC,GAAA,CAQO,MAAA,EAAA,GAAA,CAAA,EARD,KAAK,EAAC,WAAW,EAAA,CAAA,EAAA;wBACtB,GAAA,CAAmC,MAAA,EAAA,GAAA,CAAA,EAA7B,KAAK,EAAC,YAAY,EAAA,CAAA,EAAC,KAAG,CAAA;wBAC5B,GAAA,CAKE,OAAA,EAAA,GAAA,CAAA;4BAJD,KAAK,EAAC,YAAY;wCACT,IAAA,CAAA,MAAM,CAAA,UAAA,CAAA;mEAAN,IAAA,CAAA,MAAM,CAAA,UAAA,CAAA,CAAA,GAAA,MAAA,CAAA,MAAA,CAAA,KAAA,CAAA,CAAA,CAAA;4BACf,WAAW,EAAC,QAAQ;4BACpB,IAAI,EAAC,MAAM;;;oBAIb,GAAA,CAQO,MAAA,EAAA,GAAA,CAAA,EARD,KAAK,EAAC,WAAW,EAAA,CAAA,EAAA;wBACtB,GAAA,CAAkC,MAAA,EAAA,GAAA,CAAA,EAA5B,KAAK,EAAC,YAAY,EAAA,CAAA,EAAC,IAAE,CAAA;wBAC3B,GAAA,CAKE,OAAA,EAAA,GAAA,CAAA;4BAJD,KAAK,EAAC,YAAY;wCACT,IAAA,CAAA,MAAM,CAAA,UAAA,CAAA;mEAAN,IAAA,CAAA,MAAM,CAAA,UAAA,CAAA,CAAA,GAAA,MAAA,CAAA,MAAA,CAAA,KAAA,CAAA,CAAA,CAAA;4BACf,WAAW,EAAC,OAAO;4BACnB,IAAI,EAAC,UAAU;;;;gBAKlB,GAAA,CAgCO,MAAA,EAAA,GAAA,CAAA,EAhCD,KAAK,EAAC,cAAc,EAAA,CAAA,EAAA;oBACzB,GAAA,CAAuC,MAAA,EAAA,GAAA,CAAA,EAAjC,KAAK,EAAC,eAAe,EAAA,CAAA,EAAC,MAAI,CAAA;oBAEhC,GAAA,CAQO,MAAA,EAAA,GAAA,CAAA,EARD,KAAK,EAAC,WAAW,EAAA,CAAA,EAAA;wBACtB,GAAA,CAAmC,MAAA,EAAA,GAAA,CAAA,EAA7B,KAAK,EAAC,YAAY,EAAA,CAAA,EAAC,KAAG,CAAA;wBAC5B,GAAA,CAKE,OAAA,EAAA,GAAA,CAAA;4BAJD,KAAK,EAAC,YAAY;wCACT,IAAA,CAAA,MAAM,CAAA,UAAA,CAAA;mEAAN,IAAA,CAAA,MAAM,CAAA,UAAA,CAAA,CAAA,GAAA,MAAA,CAAA,MAAA,CAAA,KAAA,CAAA,CAAA,CAAA;4BACf,WAAW,EAAC,SAAS;4BACrB,IAAI,EAAC,MAAM;;;oBAIb,GAAA,CAQO,MAAA,EAAA,GAAA,CAAA,EARD,KAAK,EAAC,WAAW,EAAA,CAAA,EAAA;wBACtB,GAAA,CAMO,MAAA,EAAA,GAAA,CAAA,EAND,KAAK,EAAC,UAAU,EAAA,CAAA,EAAA;4BACrB,GAAA,CAAuC,MAAA,EAAA,GAAA,CAAA,EAAjC,KAAK,EAAC,YAAY,EAAA,CAAA,EAAC,SAAO,CAAA;4BAChC,GAAA,CAGE,iBAAA,EAAA,GAAA,CAAA;gCAFD,KAAK,EAAC,aAAa;gCACnB,OAAO,EAAE,IAAA,CAAA,MAAM,CAAA,UAAA,CAAA;;;;oBAKlB,GAAA,CAQO,MAAA,EAAA,GAAA,CAAA,EARD,KAAK,EAAC,WAAW,EAAA,CAAA,EAAA;wBACtB,GAAA,CAMO,MAAA,EAAA,GAAA,CAAA,EAND,KAAK,EAAC,UAAU,EAAA,CAAA,EAAA;4BACrB,GAAA,CAAoC,MAAA,EAAA,GAAA,CAAA,EAA9B,KAAK,EAAC,YAAY,EAAA,CAAA,EAAC,MAAI,CAAA;4BAC7B,GAAA,CAGE,iBAAA,EAAA,GAAA,CAAA;gCAFD,KAAK,EAAC,aAAa;gCACnB,OAAO,EAAE,IAAA,CAAA,MAAM,CAAA,aAAA,CAAA;;;;;gBAOnB,GAAA,CAGO,MAAA,EAAA,GAAA,CAAA,EAHD,KAAK,EAAC,cAAc,EAAA,CAAA,EAAA;oBACzB,GAAA,CAA8D,QAAA,EAAA,GAAA,CAAA;wBAAtD,KAAK,EAAC,UAAU;wBAAE,OAAK,EAAE,IAAA,CAAA,cAAc;wBAAE,MAAI,EAAA,CAAA,CAAA,WAAA,EAAA,CAAA,SAAA,CAAA,CAAA;oBACrD,GAAA,CAA0D,QAAA,EAAA,GAAA,CAAA;wBAAlD,KAAK,EAAC,UAAU;wBAAE,OAAK,EAAE,IAAA,CAAA,UAAU;wBAAE,MAAI,EAAA,CAAA,CAAA,WAAA,EAAA,CAAA,SAAA,CAAA,CAAA;;uBAIf,IAAA,CAAA,gBAAgB,CAAA;sBAAnD,GAAA,CAEO,MAAA,EAAA,GAAA,CAAA;;wBAFD,KAAK,EAAC,gBAAgB;;wBAC3B,GAAA,CAA0E,MAAA,EAAA,GAAA,CAAA;4BAApE,KAAK,EAAA,GAAA,CAAA,CAAC,aAAa,EAAS,IAAA,CAAA,WAAW,CAAA,CAAA;gCAAI,IAAA,CAAA,gBAAgB,CAAA,EAAA,CAAA,CAAA,iBAAA,CAAA", "file": "pages/config/config.uvue", "sourcesContent": ["<template>\n\t<view class=\"container\">\n\t\t<!-- 顶部导航栏 -->\n\t\t<view class=\"header\">\n\t\t\t<button class=\"back-btn\" @click=\"goBack\">返回</button>\n\t\t\t<text class=\"header-title\">WebDAV配置</text>\n\t\t\t<view class=\"header-placeholder\"></view>\n\t\t</view>\n\t\t\n\t\t<!-- 配置表单 -->\n\t\t<scroll-view class=\"config-content\" scroll-y=\"true\">\n\t\t\t<view class=\"form-container\">\n\t\t\t\t<view class=\"form-section\">\n\t\t\t\t\t<text class=\"section-title\">服务器设置</text>\n\t\t\t\t\t\n\t\t\t\t\t<view class=\"form-item\">\n\t\t\t\t\t\t<text class=\"form-label\">服务器地址</text>\n\t\t\t\t\t\t<input \n\t\t\t\t\t\t\tclass=\"form-input\" \n\t\t\t\t\t\t\tv-model=\"config['serverUrl']\" \n\t\t\t\t\t\t\tplaceholder=\"https://your-webdav-server.com\"\n\t\t\t\t\t\t\ttype=\"url\"\n\t\t\t\t\t\t/>\n\t\t\t\t\t</view>\n\t\t\t\t\t\n\t\t\t\t\t<view class=\"form-item\">\n\t\t\t\t\t\t<text class=\"form-label\">端口号</text>\n\t\t\t\t\t\t<input \n\t\t\t\t\t\t\tclass=\"form-input\" \n\t\t\t\t\t\t\tv-model=\"config['port']\" \n\t\t\t\t\t\t\tplaceholder=\"443\"\n\t\t\t\t\t\t\ttype=\"number\"\n\t\t\t\t\t\t/>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t\t\n\t\t\t\t<view class=\"form-section\">\n\t\t\t\t\t<text class=\"section-title\">认证信息</text>\n\t\t\t\t\t\n\t\t\t\t\t<view class=\"form-item\">\n\t\t\t\t\t\t<text class=\"form-label\">用户名</text>\n\t\t\t\t\t\t<input \n\t\t\t\t\t\t\tclass=\"form-input\" \n\t\t\t\t\t\t\tv-model=\"config['username']\" \n\t\t\t\t\t\t\tplaceholder=\"请输入用户名\"\n\t\t\t\t\t\t\ttype=\"text\"\n\t\t\t\t\t\t/>\n\t\t\t\t\t</view>\n\t\t\t\t\t\n\t\t\t\t\t<view class=\"form-item\">\n\t\t\t\t\t\t<text class=\"form-label\">密码</text>\n\t\t\t\t\t\t<input \n\t\t\t\t\t\t\tclass=\"form-input\" \n\t\t\t\t\t\t\tv-model=\"config['password']\" \n\t\t\t\t\t\t\tplaceholder=\"请输入密码\"\n\t\t\t\t\t\t\ttype=\"password\"\n\t\t\t\t\t\t/>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t\t\n\t\t\t\t<view class=\"form-section\">\n\t\t\t\t\t<text class=\"section-title\">高级设置</text>\n\t\t\t\t\t\n\t\t\t\t\t<view class=\"form-item\">\n\t\t\t\t\t\t<text class=\"form-label\">根目录</text>\n\t\t\t\t\t\t<input \n\t\t\t\t\t\t\tclass=\"form-input\" \n\t\t\t\t\t\t\tv-model=\"config['rootPath']\" \n\t\t\t\t\t\t\tplaceholder=\"/movies\"\n\t\t\t\t\t\t\ttype=\"text\"\n\t\t\t\t\t\t/>\n\t\t\t\t\t</view>\n\t\t\t\t\t\n\t\t\t\t\t<view class=\"form-item\">\n\t\t\t\t\t\t<view class=\"form-row\">\n\t\t\t\t\t\t\t<text class=\"form-label\">启用HTTPS</text>\n\t\t\t\t\t\t\t<switch \n\t\t\t\t\t\t\t\tclass=\"form-switch\" \n\t\t\t\t\t\t\t:checked=\"config['useHttps']\"\n\t\t\t\t\t\t\t/>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t\n\t\t\t\t\t<view class=\"form-item\">\n\t\t\t\t\t\t<view class=\"form-row\">\n\t\t\t\t\t\t\t<text class=\"form-label\">自动连接</text>\n\t\t\t\t\t\t\t<switch \n\t\t\t\t\t\t\t\tclass=\"form-switch\" \n\t\t\t\t\t\t\t:checked=\"config['autoConnect']\" \n\t\t\t\t\t\t\t/>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t\t\n\t\t\t\t<!-- 操作按钮 -->\n\t\t\t\t<view class=\"button-group\">\n\t\t\t\t\t<button class=\"test-btn\" @click=\"testConnection\">测试连接</button>\n\t\t\t\t\t<button class=\"save-btn\" @click=\"saveConfig\">保存配置</button>\n\t\t\t\t</view>\n\t\t\t\t\n\t\t\t\t<!-- 连接状态 -->\n\t\t\t\t<view class=\"status-section\" v-if=\"connectionStatus\">\n\t\t\t\t\t<text class=\"status-text\" :class=\"statusClass\">{{connectionStatus}}</text>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</scroll-view>\n\t</view>\n</template>\n\n<script>\r\n\ttype Config = {\n\t\tserverUrl: string\n\t\tport: string\n\t\tusername: string\n\t\tpassword: string\n\t\trootPath: string\n\t\tuseHttps: boolean\n\t\tautoConnect: boolean\n\t}\n\texport default {\n\t\tdata() {\n\t\t\treturn {\n\t\t\t\tconfig: {\n\t\t\t\t\tserverUrl: '',\n\t\t\t\t\tport: '443',\n\t\t\t\t\tusername: '',\n\t\t\t\t\tpassword: '',\n\t\t\t\t\trootPath: '/movies',\n\t\t\t\t\tuseHttps: true,\n\t\t\t\t\tautoConnect: false\n\t\t\t\t},\n\t\t\t\tconnectionStatus: '',\n\t\t\t\tstatusClass: ''\n\t\t\t}\n\t\t},\n\t\tonLoad() {\n\t\t\tthis.loadConfig()\n\t\t},\n\t\tmethods: {\n\t\t\tgoBack() {\n\t\t\t\tuni.navigateBack()\n\t\t\t},\n\t\t\t\n\t\t\tloadConfig() {\n\t\t\t\t// 从本地存储加载配置\n\t\t\t\ttry {\n\t\t\t\t\tconst savedConfig = uni.getStorageSync('webdav_config')\n\t\t\t\t\tif (savedConfig != null) {\n\t\t\t\t\t\tconst configData = savedConfig as UTSJSONObject\n\t\t\t\t\t\tthis.config['serverUrl'] = configData['serverUrl'] as string ?? ''\n\t\t\t\t\t\tthis.config['port'] = configData['port'] as string ?? '443'\n\t\t\t\t\t\tthis.config['username'] = configData['username'] as string ?? ''\n\t\t\t\t\t\tthis.config['password'] = configData['password'] as string ?? ''\n\t\t\t\t\t\tthis.config['rootPath'] = configData['rootPath'] as string ?? '/movies'\n\t\t\t\t\t\tthis.config['useHttps'] = configData['useHttps'] as boolean ?? true\n\t\t\t\t\t\tthis.config['autoConnect'] = configData['autoConnect'] as boolean ?? false\n\t\t\t\t\t}\n\t\t\t\t} catch (e) {\n\t\t\t\t\tconsole.error(e as any)\n\t\t\t\t}\n\t\t\t},\n\t\t\t\n\t\t\tsaveConfig() {\n\t\t\t\t// 验证必填字段\n\t\t\tif (this.config['serverUrl'] == null || (this.config['serverUrl'] as string).trim() == '') {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '请输入服务器地址',\n\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t})\n\t\t\t\t\treturn\n\t\t\t\t}\n\t\t\t\t\n\t\t\tif (this.config['username'] == null || (this.config['username'] as string).trim() == '') {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '请输入用户名',\n\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t})\n\t\t\t\t\treturn\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\t// 保存到本地存储\n\t\t\t\ttry {\n\t\t\t\t\tuni.setStorageSync('webdav_config', this.config)\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '配置保存成功',\n\t\t\t\t\t\ticon: 'success'\n\t\t\t\t\t})\n\t\t\t\t} catch (e) {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '保存失败',\n\t\t\t\t\t\ticon: 'error'\n\t\t\t\t\t})\n\t\t\t\t\tconsole.error(e as any)\n\t\t\t\t}\n\t\t\t},\n\t\t\t\n\t\t\tasync testConnection() {\n\t\t\tif ((this.config['serverUrl'] == null || (this.config['serverUrl'] as string).trim() == '') || \n\t\t\t\t(this.config['username'] == null || (this.config['username'] as string).trim() == '')) {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '请先填写服务器地址和用户名',\n\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t})\n\t\t\t\t\treturn\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\tthis.connectionStatus = '正在测试连接...'\n\t\t\t\tthis.statusClass = 'status-testing'\n\t\t\t}\n\t\t}\n\t}\n</script>\n\n<style>\n\t.container {\n\t\tflex: 1;\n\t\tbackground-color: #1a1a1a;\n\t\tmin-height: 100vh;\n\t}\n\n\t.header {\n\t\tdisplay: flex;\n\t\tjustify-content: space-between;\n\t\talign-items: center;\n\t\tpadding: 20px;\n\t\tbackground-color: #2d2d2d;\n\t\tborder-bottom: 1px solid #404040;\n\t}\n\n\t.back-btn {\n\t\tbackground-color: #404040;\n\t\tcolor: #ffffff;\n\t\tborder: none;\n\t\tborder-radius: 8px;\n\t\tpadding: 8px 16px;\n\t\tfont-size: 14px;\n\t\tcursor: pointer;\n\t}\n\n\t.back-btn:hover {\n\t\tbackground-color: #505050;\n\t}\n\n\t.header-title {\n\t\tfont-size: 20px;\n\t\tfont-weight: bold;\n\t\tcolor: #ffffff;\n\t}\n\n\t.header-placeholder {\n\t\twidth: 60px;\n\t}\n\n\t.config-content {\n\t\tflex: 1;\n\t\tpadding: 20px;\n\t}\n\n\t.form-container {\n\t\tmax-width: 600px;\n\t\tmargin: 0 auto;\n\t}\n\n\t.form-section {\n\t\tmargin-bottom: 30px;\n\t\tbackground-color: #2d2d2d;\n\t\tborder-radius: 12px;\n\t\tpadding: 20px;\n\t}\n\n\t.section-title {\n\t\tfont-size: 18px;\n\t\tfont-weight: bold;\n\t\tcolor: #ffffff;\n\t\tmargin-bottom: 20px;\n\t\tdisplay: block;\n\t}\n\n\t.form-item {\n\t\tmargin-bottom: 20px;\n\t}\n\n\t.form-item:last-child {\n\t\tmargin-bottom: 0;\n\t}\n\n\t.form-label {\n\t\tfont-size: 16px;\n\t\tcolor: #cccccc;\n\t\tmargin-bottom: 8px;\n\t\tdisplay: block;\n\t}\n\n\t.form-input {\n\t\twidth: 100%;\n\t\tpadding: 12px 16px;\n\t\tbackground-color: #404040;\n\t\tborder: 1px solid #555555;\n\t\tborder-radius: 8px;\n\t\tcolor: #ffffff;\n\t\tfont-size: 16px;\n\t}\n\n\t.form-input:focus {\n\t\tborder-color: #007AFF;\n\t\toutline: none;\n\t}\n\n\t.form-row {\n\t\tdisplay: flex;\n\t\tjustify-content: space-between;\n\t\talign-items: center;\n\t}\n\n\t.form-switch {\n\t\ttransform: scale(1.2);\n\t}\n\n\t.button-group {\n\t\tdisplay: flex;\n\t\tgap: 15px;\n\t\tmargin-top: 30px;\n\t}\n\n\t.test-btn, .save-btn {\n\t\tflex: 1;\n\t\tpadding: 15px;\n\t\tborder: none;\n\t\tborder-radius: 8px;\n\t\tfont-size: 16px;\n\t\tfont-weight: bold;\n\t\tcursor: pointer;\n\t}\n\n\t.test-btn {\n\t\tbackground-color: #FF9500;\n\t\tcolor: #ffffff;\n\t}\n\n\t.test-btn:hover {\n\t\tbackground-color: #E6850E;\n\t}\n\n\t.save-btn {\n\t\tbackground-color: #007AFF;\n\t\tcolor: #ffffff;\n\t}\n\n\t.save-btn:hover {\n\t\tbackground-color: #0056CC;\n\t}\n\n\t.status-section {\n\t\tmargin-top: 20px;\n\t\ttext-align: center;\n\t}\n\n\t.status-text {\n\t\tfont-size: 16px;\n\t\tfont-weight: bold;\n\t\tpadding: 12px 20px;\n\t\tborder-radius: 8px;\n\t\tdisplay: inline-block;\n\t}\n\n\t.status-testing {\n\t\tbackground-color: #FF9500;\n\t\tcolor: #ffffff;\n\t}\n\n\t.status-success {\n\t\tbackground-color: #34C759;\n\t\tcolor: #ffffff;\n\t}\n\n\t.status-error {\n\t\tbackground-color: #FF3B30;\n\t\tcolor: #ffffff;\n\t}\n</style>\n"]}